# Configuração do Firebase para uTulz

## 1. Configuração do Firebase Console

### Criar Projeto Firebase
1. Acesse [Firebase Console](https://console.firebase.google.com/)
2. Clique em "Adicionar projeto"
3. Nome do projeto: `utulz-6d269` (ou conforme configurado)
4. Ative Google Analytics (opcional)

### Configurar Authentication
1. No Firebase Console, vá para **Authentication** > **Sign-in method**
2. Ative o provedor **Google**
3. Configure o email de suporte do projeto
4. Adicione domínios autorizados:
   - `localhost` (para desenvolvimento)
   - Seu domínio de produção

### Configurar Firestore Database
1. No Firebase Console, vá para **Firestore Database**
2. Clique em "Criar banco de dados"
3. Escolha "Iniciar no modo de teste" (temporário)
4. Selecione uma localização (ex: `southamerica-east1`)

### Configurar Regras de Segurança
1. No Firestore, vá para **Regras**
2. Substitua as regras padrão pelo conteúdo do arquivo `firestore.rules`
3. Publique as regras

## 2. Configuração do Projeto

### Variáveis de Ambiente
As variáveis já estão configuradas no `.env.local`:

```env
NEXT_PUBLIC_FIREBASE_API_KEY="AIzaSyAy7LH4sMKQLs3b8gLApCUu7_byGNGw1AI"
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=utulz-6d269.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=utulz-6d269
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=utulz-6d269.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=************
NEXT_PUBLIC_FIREBASE_APP_ID="1:************:web:6327a7492b90d6bc61e245"
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID="G-EQ0TVTF48G"
```

### Estrutura do Banco de Dados

#### Coleção `users`
```typescript
{
  uid: string;           // ID do Firebase Auth
  email: string;         // Email do usuário
  name: string;          // Nome completo
  photoURL?: string;     // URL da foto de perfil
  createdAt: Timestamp;  // Data de criação
  lastLogin: Timestamp;  // Último login
  role?: 'admin' | 'user'; // Papel do usuário (futuro)
}
```

## 3. Comandos para Deploy

### Deploy das Regras do Firestore
```bash
# Instalar Firebase CLI
npm install -g firebase-tools

# Login no Firebase
firebase login

# Inicializar projeto (se necessário)
firebase init firestore

# Deploy das regras
firebase deploy --only firestore:rules
```

## 4. Funcionalidades Implementadas

### ✅ Autenticação
- Login com Google OAuth
- Logout
- Proteção de rotas
- Estado de loading
- Redirecionamento automático

### ✅ Banco de Dados
- Criação automática de usuários no Firestore
- Atualização do último login
- Regras de segurança configuradas

### ✅ Interface
- Página de login responsiva
- Integração com dashboard
- Dados reais do usuário na sidebar
- Estados de loading

## 5. Rotas Protegidas

- `/dashboard/*` - Requer autenticação
- `/login` - Página pública de login

## 6. Próximos Passos

1. Configurar domínios de produção no Firebase Auth
2. Implementar recuperação de senha (opcional)
3. Adicionar mais campos ao perfil do usuário
4. Implementar sistema de permissões (roles)
5. Adicionar coleções para campanhas e ferramentas
