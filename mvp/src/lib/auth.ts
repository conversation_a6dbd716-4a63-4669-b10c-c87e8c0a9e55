import { 
  signInWithPopup, 
  signOut as firebaseSignOut,
  onAuthStateChanged,
  User as FirebaseUser
} from 'firebase/auth';
import { 
  doc, 
  setDoc, 
  getDoc, 
  serverTimestamp,
  Timestamp 
} from 'firebase/firestore';
import { auth, googleProvider, db } from './firebase';
import { User, FirestoreUser } from '@/types/auth';

// Convert Firebase User to our User type
export const convertFirebaseUser = async (firebaseUser: FirebaseUser): Promise<User | null> => {
  if (!firebaseUser) return null;

  try {
    // Get user data from Firestore
    const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
    
    if (userDoc.exists()) {
      const userData = userDoc.data() as FirestoreUser;
      return {
        uid: userData.uid,
        email: userData.email,
        name: userData.name,
        photoURL: userData.photoURL,
        createdAt: userData.createdAt?.toDate() || new Date(),
        lastLogin: userData.lastLogin?.toDate() || new Date(),
        role: userData.role || 'user'
      };
    }
    
    return null;
  } catch (error) {
    console.error('Error converting Firebase user:', error);
    return null;
  }
};

// Sign in with Google
export const signInWithGoogle = async (): Promise<User | null> => {
  try {
    const result = await signInWithPopup(auth, googleProvider);
    const firebaseUser = result.user;
    
    if (!firebaseUser) throw new Error('No user returned from Google sign in');

    // Create or update user in Firestore
    const userRef = doc(db, 'users', firebaseUser.uid);
    const userDoc = await getDoc(userRef);
    
    const userData: FirestoreUser = {
      uid: firebaseUser.uid,
      email: firebaseUser.email || '',
      name: firebaseUser.displayName || '',
      photoURL: firebaseUser.photoURL || undefined,
      lastLogin: serverTimestamp(),
      role: 'user'
    };

    if (!userDoc.exists()) {
      // New user - set createdAt
      userData.createdAt = serverTimestamp();
    }

    await setDoc(userRef, userData, { merge: true });
    
    return convertFirebaseUser(firebaseUser);
  } catch (error) {
    console.error('Error signing in with Google:', error);
    throw error;
  }
};

// Sign out
export const signOut = async (): Promise<void> => {
  try {
    await firebaseSignOut(auth);
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};

// Auth state observer
export const onAuthStateChange = (callback: (user: User | null) => void) => {
  return onAuthStateChanged(auth, async (firebaseUser) => {
    if (firebaseUser) {
      const user = await convertFirebaseUser(firebaseUser);
      callback(user);
    } else {
      callback(null);
    }
  });
};
