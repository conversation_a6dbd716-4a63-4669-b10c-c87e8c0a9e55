'use client';

import React from 'react';

interface DashboardCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  icon: React.ReactNode;
  iconBgColor: string;
}

const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  iconBgColor,
}) => {
  return (
    <div className="bg-[#1a2332] border border-[#233648] rounded-lg p-6 hover:bg-[#1e2936] transition-colors cursor-pointer">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h3 className="text-[#92adc9] text-sm font-medium">{title}</h3>
            <div className={`w-6 h-6 ${iconBgColor} rounded flex items-center justify-center`}>
              {icon}
            </div>
          </div>
          <div className="text-white text-3xl font-bold mb-1">{value}</div>
          <div className="text-[#92adc9] text-sm">{subtitle}</div>
        </div>
      </div>
    </div>
  );
};

const DashboardCardsRef: React.FC = () => {
  const cards = [
    {
      title: 'Campanhas Ativas',
      value: '6',
      subtitle: '+2 esta semana',
      icon: (
        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 256 256">
          <path d="M228.54,86.66l-176.06-54A16,16,0,0,0,32,48V192a16,16,0,0,0,16,16,16,16,0,0,0,4.52-.65L76,201.05a16,16,0,0,0,10.47-15.05V176h12.85L143.64,182a16,16,0,0,0,4.36.6,16,16,0,0,0,16-16V89.35a16,16,0,0,0-12.36-15.69ZM76,185.05,48,192V48l28,8.61Zm72-18.7L92,160V80l56,16.65ZM208,112v32a8,8,0,0,1-16,0V112a8,8,0,0,1,16,0Zm32,0v32a8,8,0,0,1-16,0V112a8,8,0,0,1,16,0Z" />
        </svg>
      ),
      iconBgColor: 'bg-yellow-500',
    },
    {
      title: 'Agentes Executados',
      value: '3',
      subtitle: '15 este mês',
      icon: (
        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 256 256">
          <path d="M232,208a8,8,0,0,1-8,8H32a8,8,0,0,1-8-8V48a8,8,0,0,1,16,0v94.37L90.73,98a8,8,0,0,1,10.07-.38l58.81,44.11L218.73,90a8,8,0,1,1,10.54,12l-64,56a8,8,0,0,1-10.07.38L96.39,114.29,40,163.63V200H224A8,8,0,0,1,232,208Z" />
        </svg>
      ),
      iconBgColor: 'bg-cyan-500',
    },
    {
      title: 'Templates Salvos',
      value: '12',
      subtitle: '3 novos',
      icon: (
        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 256 256">
          <path d="M200,32H163.74a47.92,47.92,0,0,0-71.48,0H56A16,16,0,0,0,40,48V216a16,16,0,0,0,16,16H200a16,16,0,0,0,16-16V48A16,16,0,0,0,200,32Zm-72,0a32,32,0,0,1,32,32H96A32,32,0,0,1,128,32Zm72,184H56V48H72V64a8,8,0,0,0,8,8h96a8,8,0,0,0,8-8V48h16Z" />
        </svg>
      ),
      iconBgColor: 'bg-purple-500',
    },
    {
      title: 'Última Atualização',
      value: '03/09',
      subtitle: '',
      icon: (
        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 256 256">
          <path d="M208,32H184V24a8,8,0,0,0-16,0v8H88V24a8,8,0,0,0-16,0v8H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32ZM72,48v8a8,8,0,0,0,16,0V48h80v8a8,8,0,0,0,16,0V48h24V80H48V48ZM208,208H48V96H208V208Z" />
        </svg>
      ),
      iconBgColor: 'bg-orange-500',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {cards.map((card, index) => (
        <DashboardCard
          key={index}
          title={card.title}
          value={card.value}
          subtitle={card.subtitle}
          icon={card.icon}
          iconBgColor={card.iconBgColor}
        />
      ))}
    </div>
  );
};

export default DashboardCardsRef;
