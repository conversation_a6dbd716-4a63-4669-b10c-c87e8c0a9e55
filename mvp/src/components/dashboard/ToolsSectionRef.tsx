'use client';

import React from 'react';
import { Button } from '@/components/ui/Button';

interface Tool {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  iconBgColor: string;
}

const ToolsSectionRef: React.FC = () => {
  const tools: Tool[] = [
    {
      id: '1',
      title: 'Intake Radar',
      description: 'Organiz...',
      icon: (
        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 256 256">
          <path d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216Zm0-152a64,64,0,1,0,64,64A64.07,64.07,0,0,0,128,64Zm0,112a48,48,0,1,1,48-48A48.05,48.05,0,0,1,128,176Zm0-80a32,32,0,1,0,32,32A32,32,0,0,0,128,96Zm0,48a16,16,0,1,1,16-16A16,16,0,0,1,128,144Z"/>
        </svg>
      ),
      iconBgColor: 'bg-cyan-500',
    },
    {
      id: '2',
      title: 'Briefing Builder',
      description: 'Crie bri...',
      icon: (
        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 256 256">
          <path d="M200,32H163.74a47.92,47.92,0,0,0-71.48,0H56A16,16,0,0,0,40,48V216a16,16,0,0,0,16,16H200a16,16,0,0,0,16-16V48A16,16,0,0,0,200,32Zm-72,0a32,32,0,0,1,32,32H96A32,32,0,0,1,128,32Zm72,184H56V48H72V64a8,8,0,0,0,8,8h96a8,8,0,0,0,8-8V48h16Z"/>
        </svg>
      ),
      iconBgColor: 'bg-green-500',
    },
    {
      id: '3',
      title: 'Perguntas Críticas',
      description: 'Identifi...',
      icon: (
        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 256 256">
          <path d="M140,180a12,12,0,1,1-12-12A12,12,0,0,1,140,180ZM128,72c-22.06,0-40,16.15-40,36v4a8,8,0,0,0,16,0v-4c0-11,10.77-20,24-20s24,9,24,20-10.77,20-24,20a8,8,0,0,0-8,8v8a8,8,0,0,0,16,0v-.72c18.24-3.35,32-17.9,32-35.28C168,88.15,150.06,72,128,72Zm104,56A104,104,0,1,1,128,24,104.11,104.11,0,0,1,232,128Zm-16,0a88,88,0,1,0-88,88A88.1,88.1,0,0,0,216,128Z"/>
        </svg>
      ),
      iconBgColor: 'bg-orange-500',
    },
    {
      id: '4',
      title: 'Personas',
      description: 'Desenv...',
      icon: (
        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 256 256">
          <path d="M117.25,157.92a60,60,0,1,0-66.5,0A95.83,95.83,0,0,0,3.53,195.63a8,8,0,1,0,13.4,8.74,80,80,0,0,1,134.14,0,8,8,0,0,0,13.4-8.74A95.83,95.83,0,0,0,117.25,157.92ZM40,108a44,44,0,1,1,44,44A44.05,44.05,0,0,1,40,108Zm210.14,98.7a8,8,0,0,1-11.07-2.33A79.83,79.83,0,0,0,172,168a8,8,0,0,1,0-16,44,44,0,1,0-16.34-84.87,8,8,0,1,1-5.94-14.85,60,60,0,0,1,55.53,105.64,95.83,95.83,0,0,1,47.22,37.71A8,8,0,0,1,250.14,206.7Z"/>
        </svg>
      ),
      iconBgColor: 'bg-purple-500',
    },
  ];

  return (
    <div className="bg-[#1a2332] border border-[#233648] rounded-lg">
      {/* Header */}
      <div className="p-6 border-b border-[#233648]">
        <div className="flex items-center gap-2">
          <div className="w-6 h-6 bg-cyan-500 rounded flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 256 256">
              <path d="M48,64a8,8,0,0,1,8-8H72V40a8,8,0,0,1,16,0V56h16a8,8,0,0,1,0,16H88V88a8,8,0,0,1-16,0V72H56A8,8,0,0,1,48,64ZM184,192h-8v-8a8,8,0,0,0-16,0v8h-8a8,8,0,0,0,0,16h8v8a8,8,0,0,0,16,0v-8h8a8,8,0,0,0,0-16Zm56-48H224V128a8,8,0,0,0-16,0v16H192a8,8,0,0,0,0,16h16v16a8,8,0,0,0,16,0V160h16a8,8,0,0,0,0-16ZM219.31,80,80,219.31a16,16,0,0,1-22.62,0L36.68,198.63a16,16,0,0,1,0-22.63L176,36.69a16,16,0,0,1,22.63,0l20.68,20.68A16,16,0,0,1,219.31,80Zm-54.63,32L144,91.31l-96,96L68.68,208ZM208,68.69,187.31,48l-32,32L176,100.69Z"/>
            </svg>
          </div>
          <h2 className="text-white text-lg font-semibold">Agentes Rápidos</h2>
        </div>
      </div>

      {/* Tools List */}
      <div className="p-6 space-y-4">
        {tools.map((tool) => (
          <div
            key={tool.id}
            className="flex items-center gap-4 p-4 bg-[#233648] border border-[#324d67] rounded-lg hover:bg-[#2a4155] transition-colors cursor-pointer"
          >
            {/* Tool Icon */}
            <div className={`w-10 h-10 ${tool.iconBgColor} rounded-lg flex items-center justify-center flex-shrink-0`}>
              {tool.icon}
            </div>
            
            {/* Tool Info */}
            <div className="flex-1 min-w-0">
              <h3 className="text-white font-medium text-sm mb-1">{tool.title}</h3>
              <p className="text-[#92adc9] text-xs">{tool.description}</p>
            </div>
            
            {/* Arrow Icon */}
            <div className="text-[#92adc9] flex-shrink-0">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 256 256">
                <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"/>
              </svg>
            </div>
          </div>
        ))}
      </div>

      {/* Footer Button */}
      <div className="p-6 border-t border-[#233648]">
        <Button 
          variant="outline" 
          className="w-full flex items-center justify-center gap-2 text-sm"
        >
          Ver todos os agentes
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 256 256">
            <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"/>
          </svg>
        </Button>
      </div>
    </div>
  );
};

export default ToolsSectionRef;
