import { Badge } from '../ui/Badge';
import { But<PERSON> } from '../ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from "../ui/Card";

import {
  ArrowLeft,
  BarChart3,
  FileDown,
  FileText,
  Image,
  Megaphone,
  MessageSquare,
  Palette,
  Plus,
  Presentation,
  Settings,
  Share2,
  Star,
  Table,
  Users
} from "lucide-react";

const toolAreas = [
  {
    name: "Atendimento",
    icon: Users,
    color: "text-blue-500",
    tools: [
      {
        name: "Briefing inicial",
        description: "Coleta estruturada de informações do cliente e campanha.",
        type: "Docs",
        icon: FileText,
        mvp: true
      },
      {
        name: "Debriefing resumido",
        description: "Organiza devolutivas do cliente de forma objetiva.",
        type: "Docs",
        icon: FileText,
        mvp: false
      },
      {
        name: "Cronograma de entregas",
        description: "Calendário de marcos e responsáveis.",
        type: "Sheets",
        icon: Table,
        mvp: false
      },
      {
        name: "Status report",
        description: "Atualização periódica para o cliente.",
        type: "Docs",
        icon: FileText,
        mvp: false
      },
      {
        name: "Registro de aprovações",
        description: "Histórico de aprovações por etapa.",
        type: "Docs",
        icon: FileText,
        mvp: false
      },
      {
        name: "Follow-up final",
        description: "Checklist de encerramento e pendências.",
        type: "Docs",
        icon: FileText,
        mvp: false
      }
    ]
  },
  {
    name: "Planejamento",
    icon: Settings,
    color: "text-purple-500",
    tools: [
      {
        name: "Metas & KPIs",
        description: "Definição de objetivos e indicadores mensuráveis da campanha.",
        type: "Sheets",
        icon: Table,
        mvp: true
      },
      {
        name: "Plano de mídia simplificado",
        description: "Versão resumida para projetos menores.",
        type: "Sheets",
        icon: Table,
        mvp: false
      },
      {
        name: "Análise de concorrência/benchmark",
        description: "Mapeia referências e diferenciais.",
        type: "Docs",
        icon: FileText,
        mvp: false
      },
      {
        name: "Estratégia de comunicação",
        description: "Públicos, mensagens e canais prioritários.",
        type: "Docs",
        icon: FileText,
        mvp: false
      },
      {
        name: "Apresentação de planejamento",
        description: "Deck executivo para alinhamento.",
        type: "Slides",
        icon: Presentation,
        mvp: false
      },
      {
        name: "Jornada do consumidor",
        description: "Mapa das etapas e pontos de contato.",
        type: "Slides",
        icon: Presentation,
        mvp: false
      },
      {
        name: "SWOT/Diagnóstico",
        description: "Forças, fraquezas, oportunidades e ameaças.",
        type: "Docs",
        icon: FileText,
        mvp: false
      }
    ]
  },
  {
    name: "Mídia",
    icon: Megaphone,
    color: "text-orange-500",
    tools: [
      {
        name: "Plano de mídia",
        description: "Planejamento de canais, formatos, estimativas e prazos.",
        type: "Sheets",
        icon: Table,
        mvp: true
      },
      {
        name: "Estudo de canais",
        description: "Prós, contras e papel de cada canal.",
        type: "Docs",
        icon: FileText,
        mvp: false
      },
      {
        name: "Compra/negociação de mídia",
        description: "Registro de negociação e acordos.",
        type: "Docs",
        icon: FileText,
        mvp: false
      },
      {
        name: "Monitoramento de veiculação",
        description: "Acompanhamento de entregas e pacing.",
        type: "Sheets",
        icon: Table,
        mvp: false
      },
      {
        name: "Relatório de mídia",
        description: "Resultados por canal e otimizações.",
        type: "Slides",
        icon: Presentation,
        mvp: false
      }
    ]
  },
  {
    name: "Criação",
    icon: Palette,
    color: "text-pink-500",
    tools: [
      {
        name: "Conceito criativo e direcionamento do KV",
        description: "Documento com conceito, mensagem central e diretrizes do key visual.",
        type: "Docs + imagem",
        icon: FileText,
        mvp: true
      },
      {
        name: "Moodboard/Referências",
        description: "Painel visual de estilo e referências.",
        type: "Imagem",
        icon: Image,
        mvp: false
      },
      {
        name: "Roteiro de vídeo",
        description: "Estrutura de cenas e falas.",
        type: "Docs",
        icon: FileText,
        mvp: false
      },
      {
        name: "Layouts preliminares",
        description: "Esboços e primeiras variações.",
        type: "Imagem",
        icon: Image,
        mvp: false
      },
      {
        name: "Apresentação criativa",
        description: "Deck de defesa do conceito.",
        type: "Slides",
        icon: Presentation,
        mvp: false
      },
      {
        name: "Desdobramentos criativos",
        description: "Peças derivadas e guidelines.",
        type: "Docs",
        icon: FileText,
        mvp: false
      }
    ]
  },
  {
    name: "Produção",
    icon: Settings,
    color: "text-green-500",
    tools: [
      {
        name: "Produção – peça visual final",
        description: "Geração e upload do KV final em alta.",
        type: "Imagem",
        icon: Image,
        mvp: true
      },
      {
        name: "Storyboard",
        description: "Quadros com enquadramentos e ações.",
        type: "Imagem",
        icon: Image,
        mvp: false
      },
      {
        name: "Pré-produção",
        description: "Casting, locações e fornecedores.",
        type: "Docs",
        icon: FileText,
        mvp: false
      },
      {
        name: "Adaptação de formatos",
        description: "Recortes e variações finais.",
        type: "PDF",
        icon: FileDown,
        mvp: false
      },
      {
        name: "Pós-produção checklist",
        description: "Ajustes, correções e QA final.",
        type: "Docs",
        icon: FileText,
        mvp: false
      },
      {
        name: "Arquivos finais organizados",
        description: "Entrega com estrutura de pastas.",
        type: "Slides",
        icon: Presentation,
        mvp: false
      }
    ]
  },
  {
    name: "Publicação",
    icon: Share2,
    color: "text-cyan-500",
    tools: [
      {
        name: "Publicação/Acompanhamento",
        description: "Registro de status de veiculação e andamentos.",
        type: "Docs",
        icon: FileText,
        mvp: true
      },
      {
        name: "Relatório final",
        description: "Consolidação de resultados e aprendizados.",
        type: "Slides",
        icon: Presentation,
        mvp: true
      }
    ]
  },
  {
    name: "Social Media",
    icon: MessageSquare,
    color: "text-indigo-500",
    tools: [
      {
        name: "Calendário editorial",
        description: "Planejamento de posts e temas.",
        type: "Sheets",
        icon: Table,
        mvp: false
      },
      {
        name: "Posts publicados (status)",
        description: "Controle do que foi ao ar.",
        type: "Sheets",
        icon: Table,
        mvp: false
      },
      {
        name: "Monitoramento de interações",
        description: "Métricas de comentários e DMs.",
        type: "Sheets",
        icon: Table,
        mvp: false
      },
      {
        name: "Relatório de social media",
        description: "Visão executiva por rede.",
        type: "Slides",
        icon: Presentation,
        mvp: false
      }
    ]
  },
  {
    name: "BI/Monitoramento",
    icon: BarChart3,
    color: "text-red-500",
    tools: [
      {
        name: "Setup de tracking",
        description: "Checklist de tags e eventos.",
        type: "Docs",
        icon: FileText,
        mvp: false
      },
      {
        name: "Dashboards de acompanhamento",
        description: "Visão tática contínua.",
        type: "Sheets",
        icon: Table,
        mvp: false
      },
      {
        name: "Relatório parcial",
        description: "Leitura de meio de campanha.",
        type: "Slides",
        icon: Presentation,
        mvp: false
      },
      {
        name: "Relatório final consolidado",
        description: "Encerramento analítico completo.",
        type: "Slides",
        icon: Presentation,
        mvp: false
      },
      {
        name: "Recomendações futuras",
        description: "Next steps baseados nos dados.",
        type: "Docs",
        icon: FileText,
        mvp: false
      }
    ]
  },
  {
    name: "Extras",
    icon: Plus,
    color: "text-gray-500",
    tools: [
      {
        name: "Influencer mapping",
        description: "Planilha de perfis, métricas e valores.",
        type: "Sheets",
        icon: Table,
        mvp: false
      },
      {
        name: "SEO/Conteúdo plan",
        description: "Pautas, palavras-chave e calendário.",
        type: "Docs",
        icon: FileText,
        mvp: false
      },
      {
        name: "Fluxo de inbound/CRM",
        description: "Esteiras e gatilhos de automação.",
        type: "Sheets",
        icon: Table,
        mvp: false
      },
      {
        name: "Relatórios de engajamento",
        description: "Visão executiva de performance.",
        type: "Slides",
        icon: Presentation,
        mvp: false
      },
      {
        name: "Documentos de endomarketing",
        description: "Materiais internos e comunicados.",
        type: "Docs",
        icon: FileText,
        mvp: false
      },
      {
        name: "Criador de Apresentações",
        description: "Transforma conteúdo de campanha em uma apresentação Google Slides.",
        type: "Slides",
        icon: Presentation,
        mvp: true
      }
    ]
  }
];

const getTypeIcon = (type: string) => {
  switch (type) {
    case "Docs":
    case "Docs + imagem":
      return FileText;
    case "Sheets":
      return Table;
    case "Slides":
      return Presentation;
    case "Imagem":
      return Image;
    case "PDF":
      return FileDown;
    default:
      return FileText;
  }
};

const getTypeBadgeColor = (type: string) => {
  switch (type) {
    case "Docs":
    case "Docs + imagem":
      return "bg-blue-500/10 text-blue-500 border-blue-500/20";
    case "Sheets":
      return "bg-green-500/10 text-green-500 border-green-500/20";
    case "Slides":
      return "bg-orange-500/10 text-orange-500 border-orange-500/20";
    case "Imagem":
      return "bg-purple-500/10 text-purple-500 border-purple-500/20";
    case "PDF":
      return "bg-red-500/10 text-red-500 border-red-500/20";
    default:
      return "bg-gray-500/10 text-gray-500 border-gray-500/20";
  }
};

interface ToolsPageProps {
  onBackToHome: () => void;
}

export function ToolsPage({ onBackToHome }: ToolsPageProps) {
  const mvpCount = toolAreas.reduce((total, area) =>
    total + area.tools.filter(tool => tool.mvp).length, 0
  );

  const totalCount = toolAreas.reduce((total, area) =>
    total + area.tools.length, 0
  );

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="sticky top-0 z-50 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container px-4 md:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={onBackToHome}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Voltar</span>
              </Button>
              <div>
                <h1 className="text-2xl font-bold">Ferramentas uTulz</h1>
                <p className="text-muted-foreground">
                  {mvpCount} ferramentas no MVP • {totalCount} ferramentas no total
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="flex items-center space-x-1">
                <Star className="w-3 h-3" />
                <span>MVP</span>
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container px-4 md:px-8 py-8">
        <div className="space-y-12">
          {toolAreas.map((area, areaIndex) => (
            <div key={areaIndex} className="space-y-6">
              {/* Area Header */}
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg bg-muted ${area.color}`}>
                  <area.icon className="w-5 h-5" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold">{area.name}</h2>
                  <p className="text-sm text-muted-foreground">
                    {area.tools.filter(tool => tool.mvp).length} MVP • {area.tools.length} total
                  </p>
                </div>
              </div>

              {/* Tools Grid */}
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {area.tools.map((tool, toolIndex) => {
                  const TypeIcon = getTypeIcon(tool.type);

                  return (
                    <Card
                      key={toolIndex}
                      className={`relative transition-all hover:shadow-md ${tool.mvp ? 'ring-1 ring-primary/20 bg-primary/5' : ''
                        }`}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-3">
                            <div className="p-2 rounded-lg bg-muted">
                              <TypeIcon className="w-4 h-4" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <CardTitle className="text-base leading-tight">
                                {tool.name}
                              </CardTitle>
                            </div>
                          </div>
                          {tool.mvp && (
                            <Badge variant="secondary" className="ml-2 flex-shrink-0">
                              <Star className="w-3 h-3 mr-1" />
                              MVP
                            </Badge>
                          )}
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <p className="text-sm text-muted-foreground mb-3">
                          {tool.description}
                        </p>
                        <Badge
                          variant="outline"
                          className={`text-xs ${getTypeBadgeColor(tool.type)}`}
                        >
                          {tool.type}
                        </Badge>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Footer Stats */}
        <div className="mt-16 pt-8 border-t">
          <div className="grid md:grid-cols-3 gap-6 text-center">
            <div>
              <div className="text-2xl font-bold text-primary">{mvpCount}</div>
              <div className="text-sm text-muted-foreground">Ferramentas no MVP</div>
            </div>
            <div>
              <div className="text-2xl font-bold">{totalCount}</div>
              <div className="text-sm text-muted-foreground">Total de Ferramentas</div>
            </div>
            <div>
              <div className="text-2xl font-bold">{toolAreas.length}</div>
              <div className="text-sm text-muted-foreground">Áreas de Atuação</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}