"use client";
import { Logo } from "@/components/branding/Logo";
import { Button } from "@/components/ui/Button";
import { Container } from "@/components/ui/Container";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { Menu, X } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { AuthDialog } from "./auth/AuthDialog";

export function Header({ onNavigateToTools, onNavigateToPricing }: { onNavigateToTools: () => void; onNavigateToPricing: () => void }) {
    const [open, setOpen] = useState(false);
    const [isMenuOpen, setIsMenuOpen] = useState(false);

    const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

    return (
        <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <Container className="flex h-16 items-center justify-between">
                {/* Logo */}
                <Link href="/" className="flex items-center">
                    <Logo />
                </Link>

                {/* Desktop Navigation */}
                <nav className="hidden md:flex items-center space-x-8">
                    <Link
                        href="/#ferramentas"
                        className="text-foreground hover:text-primary transition-colors"
                    >
                        Ferramentas
                    </Link>
                    <Link
                        href="/preco"
                        className="text-foreground hover:text-primary transition-colors"
                    >
                        Preços
                    </Link>
                    <Link
                        href="/campanha"
                        className="text-foreground hover:text-primary transition-colors"
                    >
                        Campanha
                    </Link>
                </nav>

                {/* CTA Buttons */}
                <div className="hidden md:flex items-center space-x-4">
                    <ThemeToggle />
                    <Button variant="outline" onClick={() => setOpen(true)}>Entrar</Button>
                    <Button onClick={() => setOpen(true)}>Começar Grátis</Button>
                </div>

                {/* Mobile Menu Button */}
                <button
                    className="md:hidden"
                    onClick={toggleMenu}
                    aria-label="Menu"
                >
                    {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
                </button>
            </Container>

            {/* Mobile Menu */}
            {isMenuOpen && (
                <div className="md:hidden border-t bg-background">
                    <Container className="py-4 space-y-4">
                        <Link
                            href="/#ferramentas"
                            className="block text-foreground hover:text-primary transition-colors"
                            onClick={() => setIsMenuOpen(false)}
                        >
                            Ferramentas
                        </Link>
                        <Link
                            href="/preco"
                            className="block text-foreground hover:text-primary transition-colors"
                            onClick={() => setIsMenuOpen(false)}
                        >
                            Preços
                        </Link>
                        <Link
                            href="/campanha"
                            className="block text-foreground hover:text-primary transition-colors"
                            onClick={() => setIsMenuOpen(false)}
                        >
                            Campanha
                        </Link>
                        <div className="flex flex-col space-y-2 pt-4">
                            <div className="flex justify-center pb-2">
                                <ThemeToggle />
                            </div>
                            <Button variant="outline" className="w-full" onClick={() => setOpen(true)}>Entrar</Button>
                            <Button className="w-full" onClick={() => setOpen(true)}>Começar Grátis</Button>
                        </div>
                    </Container>
                </div>
            )}

            <AuthDialog open={open} onClose={() => setOpen(false)} />
        </header>
    );
}
