"use client";

import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { Container } from "@/components/ui/Container";
import { ArrowRight, CheckCircle, Sparkles } from "lucide-react";
import Link from "next/link";

interface CTASectionProps {
  onNavigateToPricing?: () => void;
}

const features = [
  "Acesso a todas as ferramentas de IA",
  "Suporte prioritário 24/7",
  "Integrações ilimitadas",
  "Relatórios avançados",
  "Treinamento personalizado"
];

export function CTASection({ onNavigateToPricing }: CTASectionProps) {
  return (
    <section className="py-20 md:py-32" data-pricing-cta>
      <Container>
        <Card className="relative overflow-hidden bg-linear-to-br from-primary to-primary/80 text-primary-foreground">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-grid-white/10 mask-[linear-gradient(0deg,white,rgba(255,255,255,0.6))]" />

          <div className="relative p-8 md:p-12 lg:p-16">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              {/* Left Content */}
              <div className="space-y-8">
                <Badge variant="secondary" className="w-fit bg-white/20 text-white border-white/30">
                  <Sparkles className="w-3 h-3 mr-2" />
                  Oferta Especial
                </Badge>

                <div className="space-y-6">
                  <h2 className="text-3xl md:text-4xl lg:text-5xl tracking-tight">
                    Pronto para{" "}
                    <span className="text-white">revolucionar</span>{" "}
                    sua agência?
                  </h2>

                  <p className="text-xl text-primary-foreground/90 max-w-lg">
                    Junte-se a centenas de agências que já transformaram suas operações
                    com a uTulz. Comece gratuitamente hoje mesmo.
                  </p>
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <Link href="/#testar">
                    <Button size="lg" variant="secondary" className="group bg-white text-primary hover:bg-white/90">
                      Começar Grátis Agora
                      <ArrowRight className="w-4 h-4 ml-2 transition-transform group-hover:translate-x-1" />
                    </Button>
                  </Link>
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-white/30 text-white hover:bg-white/10"
                    onClick={onNavigateToPricing}
                  >
                    Ver Planos
                  </Button>
                </div>

                {/* Trust Indicators */}
                <div className="flex items-center space-x-6 pt-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">500+</div>
                    <div className="text-sm text-primary-foreground/80">Agências</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">10k+</div>
                    <div className="text-sm text-primary-foreground/80">Campanhas</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">99%</div>
                    <div className="text-sm text-primary-foreground/80">Satisfação</div>
                  </div>
                </div>
              </div>

              {/* Right Content - Features List */}
              <div className="space-y-6">
                <h3 className="text-2xl font-semibold">O que você ganha:</h3>

                <ul className="space-y-4">
                  {features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-3">
                      <CheckCircle className="w-5 h-5 text-green-400 shrink-0" />
                      <span className="text-primary-foreground/90">{feature}</span>
                    </li>
                  ))}
                </ul>

                <div className="pt-6 border-t border-white/20">
                  <div className="flex items-center space-x-2 text-sm text-primary-foreground/80">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span>Sem compromisso • Cancele quando quiser</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </Container>
    </section>
  );
}
