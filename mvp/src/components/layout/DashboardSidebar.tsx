"use client";

import { Logo } from "@/components/branding/Logo";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/Avatar";
import { Button } from "@/components/ui/Button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/DropdownMenu";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import {
  File, Home,
  LogOut,
  Megaphone,
  Puzzle,
  Settings,
  Users,
  Wrench
} from "lucide-react";
import { useState } from "react";

interface NavigationItem {
  icon: React.ElementType;
  label: string;
  href: string;
  active?: boolean;
}

const navigationItems: NavigationItem[] = [
  { icon: Home, label: "Dashboard", href: "/dashboard", active: true },
  { icon: Users, label: "Clientes", href: "/clientes" },
  { icon: Megaphone, label: "Campanhas", href: "/campanhas" },
  { icon: Wrench, label: "Ferramentas", href: "/ferramentas" },
  { icon: File, label: "Templates", href: "/templates" },
  { icon: Puzzle, label: "Integrações", href: "/integracoes" },
];

interface DashboardSidebarProps {
  className?: string;
}

export function DashboardSidebar({ className }: DashboardSidebarProps) {
  const [activeItem, setActiveItem] = useState("/dashboard");

  // Mock user data - in a real app, this would come from context/props
  const user = {
    name: "João Silva",
    role: "Diretor de Criação",
    agency: "Agência Digital Pro",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    initials: "JS",
  };

  const handleSignOut = () => {
    // Handle sign out logic here
    console.log("Signing out...");
  };

  return (
    <div className={cn("flex h-screen w-64 flex-col bg-card border-r shadow-sm", className)}>
      {/* Header Section */}
      <div className="flex flex-col p-6 space-y-4">
        {/* Logo */}
        <Logo />

        {/* Agency and User Info */}
        <div className="space-y-2">
          <div className="text-sm font-medium text-foreground truncate">{user.agency}</div>
          <div className="text-sm text-muted-foreground truncate">{user.name}</div>
          <div className="text-xs text-muted-foreground truncate">{user.role}</div>
        </div>

        {/* Divider */}
        <Separator />
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 px-4 space-y-1">
        {navigationItems.map((item) => {
          const isActive = activeItem === item.href;
          return (
            <Button
              key={item.href}
              variant={isActive ? "default" : "ghost"}
              className={cn(
                "w-full justify-start gap-3 h-10 transition-all duration-200",
                isActive
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "hover:bg-accent hover:text-accent-foreground"
              )}
              onClick={() => setActiveItem(item.href)}
            >
              <item.icon className="h-4 w-4" />
              <span className="truncate">{item.label}</span>
            </Button>
          );
        })}
      </nav>

      {/* Footer Section - User Profile */}
      <div className="p-4 border-t">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="w-full justify-start gap-3 h-12 p-3 hover:bg-accent transition-colors duration-200"
            >
              <Avatar className="h-8 w-8">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback className="bg-primary text-primary-foreground">
                  {user.initials}
                </AvatarFallback>
              </Avatar>
              <div className="flex flex-col items-start text-left min-w-0 flex-1">
                <span className="text-sm font-medium truncate">{user.name}</span>
                <span className="text-xs text-muted-foreground truncate">Ver perfil</span>
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align="end" forceMount>
            <DropdownMenuItem className="flex items-center gap-2 cursor-pointer">
              <Settings className="h-4 w-4" />
              <span>Configurações</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="flex items-center gap-2 text-destructive focus:text-destructive cursor-pointer"
              onClick={handleSignOut}
            >
              <LogOut className="h-4 w-4" />
              <span>Sair</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
