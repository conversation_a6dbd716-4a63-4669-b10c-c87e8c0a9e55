'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { BriefingOutputs } from '@/types/campaign';

interface BriefingResultsProps {
  results: BriefingOutputs;
  onEdit: () => void;
  onContinue: () => void;
  onExecuteAgain: () => void;
}

const BriefingResults: React.FC<BriefingResultsProps> = ({
  results,
  onEdit,
  onContinue,
  onExecuteAgain
}) => {
  const [editableContent, setEditableContent] = useState(results.briefingDraft.htmlContent);
  const [isEditing, setIsEditing] = useState(false);

  const handleSaveEdit = () => {
    // TODO: Save edited content
    setIsEditing(false);
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-white mb-2">Resultados do Agente de Briefing</h1>
        <p className="text-[#92adc9]">
          Revise os pontos levantados e o rascunho do briefing gerado pelo agente.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column - Points Raised */}
        <div className="space-y-6">
          {/* Doubts */}
          <div className="bg-[#1a2332] border border-[#233648] rounded-lg p-6">
            <h3 className="text-white text-lg font-semibold mb-4 flex items-center gap-2">
              <svg className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 256 256">
                <path d="M140,180a12,12,0,1,1-12-12A12,12,0,0,1,140,180ZM128,72c-22.06,0-40,16.15-40,36v4a8,8,0,0,0,16,0v-4c0-11,10.77-20,24-20s24,9,24,20-10.77,20-24,20a8,8,0,0,0-8,8v8a8,8,0,0,0,16,0v-.72c18.24-3.35,32-17.9,32-35.28C168,88.15,150.06,72,128,72Zm104,56A104,104,0,1,1,128,24,104.11,104.11,0,0,1,232,128Zm-16,0a88,88,0,1,0-88,88A88.1,88.1,0,0,0,216,128Z"/>
              </svg>
              Dúvidas
            </h3>
            <ul className="space-y-2">
              {results.pointsRaised.doubts.map((doubt, index) => (
                <li key={index} className="text-[#92adc9] text-sm flex items-start gap-2">
                  <span className="text-yellow-400 mt-1">•</span>
                  {doubt}
                </li>
              ))}
            </ul>
          </div>

          {/* Missing Items */}
          <div className="bg-[#1a2332] border border-[#233648] rounded-lg p-6">
            <h3 className="text-white text-lg font-semibold mb-4 flex items-center gap-2">
              <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 256 256">
                <path d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216Zm0-144a12,12,0,1,1-12,12A12,12,0,0,1,128,72Zm0,56a8,8,0,0,0-8,8v56a8,8,0,0,0,16,0V136A8,8,0,0,0,128,128Z"/>
              </svg>
              Itens Faltantes
            </h3>
            <ul className="space-y-2">
              {results.pointsRaised.missingItems.map((item, index) => (
                <li key={index} className="text-[#92adc9] text-sm flex items-start gap-2">
                  <span className="text-red-400 mt-1">•</span>
                  {item}
                </li>
              ))}
            </ul>
          </div>

          {/* Critical Questions */}
          <div className="bg-[#1a2332] border border-[#233648] rounded-lg p-6">
            <h3 className="text-white text-lg font-semibold mb-4 flex items-center gap-2">
              <svg className="w-5 h-5 text-purple-400" fill="currentColor" viewBox="0 0 256 256">
                <path d="M140,180a12,12,0,1,1-12-12A12,12,0,0,1,140,180ZM128,72c-22.06,0-40,16.15-40,36v4a8,8,0,0,0,16,0v-4c0-11,10.77-20,24-20s24,9,24,20-10.77,20-24,20a8,8,0,0,0-8,8v8a8,8,0,0,0,16,0v-.72c18.24-3.35,32-17.9,32-35.28C168,88.15,150.06,72,128,72Zm104,56A104,104,0,1,1,128,24,104.11,104.11,0,0,1,232,128Zm-16,0a88,88,0,1,0-88,88A88.1,88.1,0,0,0,216,128Z"/>
              </svg>
              Perguntas Críticas
            </h3>
            <ul className="space-y-2">
              {results.pointsRaised.criticalQuestions.map((question, index) => (
                <li key={index} className="text-[#92adc9] text-sm flex items-start gap-2">
                  <span className="text-purple-400 mt-1">•</span>
                  {question}
                </li>
              ))}
            </ul>
          </div>

          {/* Opportunities */}
          <div className="bg-[#1a2332] border border-[#233648] rounded-lg p-6">
            <h3 className="text-white text-lg font-semibold mb-4 flex items-center gap-2">
              <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 256 256">
                <path d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"/>
              </svg>
              Oportunidades
            </h3>
            <ul className="space-y-2">
              {results.pointsRaised.opportunities.map((opportunity, index) => (
                <li key={index} className="text-[#92adc9] text-sm flex items-start gap-2">
                  <span className="text-green-400 mt-1">•</span>
                  {opportunity}
                </li>
              ))}
            </ul>
          </div>

          {/* Challenges */}
          <div className="bg-[#1a2332] border border-[#233648] rounded-lg p-6">
            <h3 className="text-white text-lg font-semibold mb-4 flex items-center gap-2">
              <svg className="w-5 h-5 text-orange-400" fill="currentColor" viewBox="0 0 256 256">
                <path d="M236.8,188.09,149.35,36.22h0a24.76,24.76,0,0,0-42.7,0L19.2,188.09a23.51,23.51,0,0,0,0,23.72A24.35,24.35,0,0,0,40.55,224h174.9a24.35,24.35,0,0,0,21.33-12.19A23.51,23.51,0,0,0,236.8,188.09ZM222.93,203.8a8.5,8.5,0,0,1-7.48,4.2H40.55a8.5,8.5,0,0,1-7.48-4.2,7.59,7.59,0,0,1,0-7.72L120.52,44.21a8.75,8.75,0,0,1,15,0l87.45,151.87A7.59,7.59,0,0,1,222.93,203.8ZM120,144V104a8,8,0,0,1,16,0v40a8,8,0,0,1-16,0Zm20,36a12,12,0,1,1-12-12A12,12,0,0,1,140,180Z"/>
              </svg>
              Desafios
            </h3>
            <ul className="space-y-2">
              {results.pointsRaised.challenges.map((challenge, index) => (
                <li key={index} className="text-[#92adc9] text-sm flex items-start gap-2">
                  <span className="text-orange-400 mt-1">•</span>
                  {challenge}
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Right Column - Briefing Draft */}
        <div className="bg-[#1a2332] border border-[#233648] rounded-lg">
          {/* Header */}
          <div className="p-6 border-b border-[#233648]">
            <div className="flex items-center justify-between">
              <h3 className="text-white text-lg font-semibold">Rascunho do Briefing</h3>
              <div className="flex gap-2">
                {isEditing ? (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsEditing(false)}
                    >
                      Cancelar
                    </Button>
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={handleSaveEdit}
                    >
                      Salvar
                    </Button>
                  </>
                ) : (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(true)}
                    className="flex items-center gap-2"
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 256 256">
                      <path d="M227.31,73.37,182.63,28.69a16,16,0,0,0-22.63,0L36.69,152A15.86,15.86,0,0,0,32,163.31V208a16,16,0,0,0,16,16H92.69A15.86,15.86,0,0,0,104,219.31L227.31,96a16,16,0,0,0,0-22.63ZM92.69,208H48V163.31l88-88L180.69,120ZM192,108.69,147.31,64l24-24L216,84.69Z"/>
                    </svg>
                    Editar
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {isEditing ? (
              <textarea
                value={editableContent}
                onChange={(e) => setEditableContent(e.target.value)}
                className="w-full h-96 px-3 py-2 bg-[#233648] border border-[#324d67] rounded-lg text-white placeholder-[#92adc9] focus:outline-none focus:ring-2 focus:ring-[#1172d4] resize-none font-mono text-sm"
              />
            ) : (
              <div 
                className="prose prose-invert max-w-none text-sm"
                dangerouslySetInnerHTML={{ __html: editableContent }}
              />
            )}
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between mt-8">
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={onEdit}
          >
            Editar Inputs
          </Button>
          <Button
            variant="outline"
            onClick={onExecuteAgain}
          >
            Executar Novamente
          </Button>
        </div>

        <Button
          variant="primary"
          onClick={onContinue}
          className="flex items-center gap-2"
        >
          Continuar para Próxima Etapa
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 256 256">
            <path d="M221.66,133.66l-72,72a8,8,0,0,1-11.32-11.32L196.69,136H40a8,8,0,0,1,0-16H196.69L138.34,61.66a8,8,0,0,1,11.32-11.32l72,72A8,8,0,0,1,221.66,133.66Z"/>
          </svg>
        </Button>
      </div>
    </div>
  );
};

export default BriefingResults;
