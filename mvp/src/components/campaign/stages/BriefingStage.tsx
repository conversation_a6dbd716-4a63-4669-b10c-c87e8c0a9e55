'use client';

import { Button } from '@/components/ui/Button';
import { BriefingInputs } from '@/types/campaign';
import React, { useState } from 'react';

interface BriefingStageProps {
  campaignData: any;
  onComplete: (data: any) => void;
  onBack: () => void;
}

const BriefingStage: React.FC<BriefingStageProps> = ({
  campaignData,
  onComplete,
  onBack
}) => {
  const [inputs, setInputs] = useState<BriefingInputs>({
    campaignName: campaignData.name || '',
    client: campaignData.client || '',
    date: '',
    budget: campaignData.budget || undefined,
    targetAudience: campaignData.targetAudience || '',
    creativeDirection: '',
    description: ''
  });

  const [errors, setErrors] = useState<Partial<BriefingInputs>>({});
  const [isExecuting, setIsExecuting] = useState(false);
  const [results, setResults] = useState<BriefingOutputs | null>(null);
  const [showResults, setShowResults] = useState(false);

  const handleInputChange = (field: keyof BriefingInputs, value: string | number) => {
    setInputs(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<BriefingInputs> = {};

    if (!inputs.description.trim()) {
      newErrors.description = 'Descrição é obrigatória';
    } else if (inputs.description.trim().split(' ').length < 10) {
      newErrors.description = 'Descrição deve ter no mínimo 10 palavras';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleExecuteAgent = async () => {
    if (!validateForm()) return;

    setIsExecuting(true);
    try {
      // TODO: Implement agent execution API call
      console.log('Executing briefing agent with inputs:', inputs);

      // Simulate agent execution
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Mock response data
      const mockResponse = {
        pointsRaised: {
          doubts: [
            'Qual é o posicionamento atual da marca no mercado?',
            'Existem campanhas similares da concorrência?'
          ],
          missingItems: [
            'Definição clara do tom de voz',
            'Especificação de canais de distribuição'
          ],
          criticalQuestions: [
            'Qual é o diferencial competitivo do produto?',
            'Como medir o sucesso da campanha?'
          ],
          opportunities: [
            'Mercado em crescimento de 15% ao ano',
            'Baixa saturação publicitária no segmento'
          ],
          challenges: [
            'Concorrência estabelecida',
            'Orçamento limitado para mídia'
          ]
        },
        briefingDraft: {
          title: `Briefing - ${inputs.campaignName || 'Nova Campanha'}`,
          htmlContent: `
            <div class="briefing-document">
              <h1>Briefing de Campanha</h1>
              <h2>Informações Gerais</h2>
              <p><strong>Cliente:</strong> ${inputs.client || 'A definir'}</p>
              <p><strong>Campanha:</strong> ${inputs.campaignName || 'Nova Campanha'}</p>
              <p><strong>Orçamento:</strong> ${inputs.budget ? `R$ ${inputs.budget.toLocaleString()}` : 'A definir'}</p>

              <h2>Objetivo</h2>
              <p>${inputs.description}</p>

              <h2>Público-Alvo</h2>
              <p>${inputs.targetAudience || 'A definir'}</p>

              <h2>Direcionamento Criativo</h2>
              <p>${inputs.creativeDirection || 'A definir'}</p>
            </div>
          `,
          sections: []
        }
      };

      setResults(mockResponse);
      setShowResults(true);
    } catch (error) {
      console.error('Error executing agent:', error);
    } finally {
      setIsExecuting(false);
    }
  };

  // Show results if agent has been executed
  if (showResults && results) {
    return (
      <BriefingResults
        results={results}
        onEdit={() => setShowResults(false)}
        onContinue={() => onComplete(results)}
        onExecuteAgain={() => {
          setResults(null);
          setShowResults(false);
        }}
      />
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-white mb-2">Criação do Briefing</h1>
        <p className="text-[#92adc9]">
          Preencha as informações abaixo para que nosso agente possa criar um briefing completo para sua campanha.
        </p>
      </div>

      {/* Form */}
      <div className="bg-[#1a2332] border border-[#233648] rounded-lg p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Campaign Name */}
          <div>
            <label className="block text-white text-sm font-medium mb-2">
              Nome da Campanha (opcional)
            </label>
            <input
              type="text"
              value={inputs.campaignName}
              onChange={(e) => handleInputChange('campaignName', e.target.value)}
              placeholder="Ex: Lançamento Produto X"
              className="w-full px-3 py-2 bg-[#233648] border border-[#324d67] rounded-lg text-white placeholder-[#92adc9] focus:outline-none focus:ring-2 focus:ring-[#1172d4]"
            />
          </div>

          {/* Client */}
          <div>
            <label className="block text-white text-sm font-medium mb-2">
              Cliente (opcional)
            </label>
            <input
              type="text"
              value={inputs.client}
              onChange={(e) => handleInputChange('client', e.target.value)}
              placeholder="Nome do cliente"
              className="w-full px-3 py-2 bg-[#233648] border border-[#324d67] rounded-lg text-white placeholder-[#92adc9] focus:outline-none focus:ring-2 focus:ring-[#1172d4]"
            />
          </div>

          {/* Date */}
          <div>
            <label className="block text-white text-sm font-medium mb-2">
              Data (opcional)
            </label>
            <input
              type="date"
              value={inputs.date}
              onChange={(e) => handleInputChange('date', e.target.value)}
              className="w-full px-3 py-2 bg-[#233648] border border-[#324d67] rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#1172d4]"
            />
          </div>

          {/* Budget */}
          <div>
            <label className="block text-white text-sm font-medium mb-2">
              Verba (opcional)
            </label>
            <input
              type="number"
              value={inputs.budget || ''}
              onChange={(e) => handleInputChange('budget', e.target.value ? Number(e.target.value) : '')}
              placeholder="Ex: 50000"
              className="w-full px-3 py-2 bg-[#233648] border border-[#324d67] rounded-lg text-white placeholder-[#92adc9] focus:outline-none focus:ring-2 focus:ring-[#1172d4]"
            />
          </div>

          {/* Target Audience */}
          <div className="md:col-span-2">
            <label className="block text-white text-sm font-medium mb-2">
              Público-alvo (opcional)
            </label>
            <textarea
              value={inputs.targetAudience}
              onChange={(e) => handleInputChange('targetAudience', e.target.value)}
              placeholder="Descreva o público-alvo da campanha..."
              rows={3}
              className="w-full px-3 py-2 bg-[#233648] border border-[#324d67] rounded-lg text-white placeholder-[#92adc9] focus:outline-none focus:ring-2 focus:ring-[#1172d4] resize-none"
            />
          </div>

          {/* Creative Direction */}
          <div className="md:col-span-2">
            <label className="block text-white text-sm font-medium mb-2">
              Direcionamento Criativo (opcional)
            </label>
            <textarea
              value={inputs.creativeDirection}
              onChange={(e) => handleInputChange('creativeDirection', e.target.value)}
              placeholder="Descreva o direcionamento criativo desejado..."
              rows={3}
              className="w-full px-3 py-2 bg-[#233648] border border-[#324d67] rounded-lg text-white placeholder-[#92adc9] focus:outline-none focus:ring-2 focus:ring-[#1172d4] resize-none"
            />
          </div>

          {/* Description - Required */}
          <div className="md:col-span-2">
            <label className="block text-white text-sm font-medium mb-2">
              Campo Descritivo *
            </label>
            <textarea
              value={inputs.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Descreva detalhadamente o que você precisa para esta campanha... (mínimo 10 palavras)"
              rows={4}
              className={`w-full px-3 py-2 bg-[#233648] border rounded-lg text-white placeholder-[#92adc9] focus:outline-none focus:ring-2 focus:ring-[#1172d4] resize-none ${errors.description ? 'border-red-500' : 'border-[#324d67]'
                }`}
            />
            {errors.description && (
              <p className="text-red-400 text-sm mt-1">{errors.description}</p>
            )}
            <p className="text-[#92adc9] text-xs mt-1">
              Palavras: {inputs.description.trim().split(' ').filter(word => word.length > 0).length}/10 mínimo
            </p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={onBack}
          disabled={isExecuting}
        >
          Voltar
        </Button>

        <Button
          variant="primary"
          onClick={handleExecuteAgent}
          disabled={isExecuting}
          className="flex items-center gap-2"
        >
          {isExecuting ? (
            <>
              <svg className="w-4 h-4 animate-spin" fill="currentColor" viewBox="0 0 256 256">
                <path d="M232,128a104,104,0,0,1-208,0c0-41,23.81-78.36,60.66-95.27a8,8,0,0,1,6.68,14.54C60.15,61.59,40,93.27,40,128a88,88,0,0,0,176,0c0-34.73-20.15-66.41-51.34-80.73a8,8,0,0,1,6.68-14.54C208.19,49.64,232,87,232,128Z" />
              </svg>
              Executando Agente...
            </>
          ) : (
            'Executar Agente de Briefing'
          )}
        </Button>
      </div>
    </div>
  );
};

export default BriefingStage;
