'use client';

import React from 'react';
import { CampaignStage } from '@/types/campaign';

interface Stage {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'skipped';
}

interface CampaignStageLayoutProps {
  campaignName: string;
  stages: Stage[];
  currentStage: CampaignStage;
  onStageClick: (stage: CampaignStage) => void;
  children: React.ReactNode;
}

const CampaignStageLayout: React.FC<CampaignStageLayoutProps> = ({
  campaignName,
  stages,
  currentStage,
  onStageClick,
  children
}) => {
  const getStageIcon = (status: Stage['status']) => {
    switch (status) {
      case 'completed':
        return (
          <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 256 256">
            <path d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"/>
          </svg>
        );
      case 'in_progress':
        return (
          <svg className="w-5 h-5 text-blue-400 animate-spin" fill="currentColor" viewBox="0 0 256 256">
            <path d="M232,128a104,104,0,0,1-208,0c0-41,23.81-78.36,60.66-95.27a8,8,0,0,1,6.68,14.54C60.15,61.59,40,93.27,40,128a88,88,0,0,0,176,0c0-34.73-20.15-66.41-51.34-80.73a8,8,0,0,1,6.68-14.54C208.19,49.64,232,87,232,128Z"/>
          </svg>
        );
      case 'pending':
        return (
          <div className="w-5 h-5 border-2 border-[#92adc9] rounded-full"></div>
        );
      case 'skipped':
        return (
          <svg className="w-5 h-5 text-[#92adc9]" fill="currentColor" viewBox="0 0 256 256">
            <path d="M205.66,194.34a8,8,0,0,1-11.32,11.32L128,139.31,61.66,205.66a8,8,0,0,1-11.32-11.32L116.69,128,50.34,61.66A8,8,0,0,1,61.66,50.34L128,116.69l66.34-66.35a8,8,0,0,1,11.32,11.32L139.31,128Z"/>
          </svg>
        );
    }
  };

  const getStageColor = (stage: Stage, isCurrent: boolean) => {
    if (isCurrent) return 'bg-[#1172d4] border-[#1172d4] text-white';
    
    switch (stage.status) {
      case 'completed':
        return 'bg-green-500/20 border-green-500 text-green-400';
      case 'in_progress':
        return 'bg-blue-500/20 border-blue-500 text-blue-400';
      case 'pending':
        return 'bg-[#233648] border-[#324d67] text-[#92adc9] hover:bg-[#2a4155]';
      case 'skipped':
        return 'bg-[#233648] border-[#324d67] text-[#92adc9]';
      default:
        return 'bg-[#233648] border-[#324d67] text-[#92adc9]';
    }
  };

  return (
    <div className="min-h-screen bg-[#111a22] text-white">
      {/* Header */}
      <div className="border-b border-[#233648] bg-[#0f1419]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <button
                onClick={() => window.history.back()}
                className="p-2 text-[#92adc9] hover:text-white hover:bg-[#233648] rounded transition-colors"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 256 256">
                  <path d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z"/>
                </svg>
              </button>
              <div>
                <h1 className="text-xl font-bold text-white">{campaignName}</h1>
                <p className="text-sm text-[#92adc9]">Criação de campanha</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stage Progress */}
      <div className="border-b border-[#233648] bg-[#1a2332]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            {stages.map((stage, index) => {
              const isCurrent = stage.id === currentStage;
              const isClickable = stage.status !== 'pending' || isCurrent;
              
              return (
                <div key={stage.id} className="flex items-center">
                  {/* Stage Button */}
                  <button
                    onClick={() => isClickable && onStageClick(stage.id as CampaignStage)}
                    disabled={!isClickable}
                    className={`flex items-center gap-3 px-4 py-2 rounded-lg border transition-colors ${getStageColor(stage, isCurrent)} ${
                      isClickable ? 'cursor-pointer' : 'cursor-not-allowed opacity-50'
                    }`}
                  >
                    {getStageIcon(stage.status)}
                    <div className="text-left">
                      <div className="font-medium text-sm">{stage.name}</div>
                      <div className="text-xs opacity-75">{stage.description}</div>
                    </div>
                  </button>

                  {/* Connector Line */}
                  {index < stages.length - 1 && (
                    <div className="w-8 h-px bg-[#324d67] mx-2"></div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children}
      </div>
    </div>
  );
};

export default CampaignStageLayout;
