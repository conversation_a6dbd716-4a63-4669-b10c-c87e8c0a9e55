'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { CreateCampaignForm } from '@/types/campaign';

interface CreateCampaignModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateCampaignForm) => void;
  isLoading?: boolean;
}

const CreateCampaignModal: React.FC<CreateCampaignModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading = false
}) => {
  const [formData, setFormData] = useState<CreateCampaignForm>({
    name: '',
    client: '',
    objective: '',
    targetAudience: '',
    budget: undefined,
    startDate: '',
    endDate: ''
  });

  const [errors, setErrors] = useState<Partial<CreateCampaignForm>>({});

  const handleInputChange = (field: keyof CreateCampaignForm, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<CreateCampaignForm> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nome da campanha é obrigatório';
    }

    if (!formData.objective.trim()) {
      newErrors.objective = 'Objetivo principal é obrigatório';
    }

    if (!formData.targetAudience.trim()) {
      newErrors.targetAudience = 'Público-alvo é obrigatório';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const handleCancel = () => {
    setFormData({
      name: '',
      client: '',
      objective: '',
      targetAudience: '',
      budget: undefined,
      startDate: '',
      endDate: ''
    });
    setErrors({});
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-[#1a2332] border border-[#233648] rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-[#233648]">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-white text-xl font-bold mb-1">Criar Nova Campanha</h2>
              <p className="text-[#92adc9] text-sm">Vamos começar com as informações básicas</p>
            </div>
            <button
              onClick={handleCancel}
              className="text-[#92adc9] hover:text-white transition-colors"
            >
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 256 256">
                <path d="M205.66,194.34a8,8,0,0,1-11.32,11.32L128,139.31,61.66,205.66a8,8,0,0,1-11.32-11.32L116.69,128,50.34,61.66A8,8,0,0,1,61.66,50.34L128,116.69l66.34-66.35a8,8,0,0,1,11.32,11.32L139.31,128Z"/>
              </svg>
            </button>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Campaign Information Section */}
          <div>
            <h3 className="text-white text-lg font-semibold mb-4">Informações da Campanha</h3>
            
            {/* Campaign Name */}
            <div className="mb-4">
              <label className="block text-white text-sm font-medium mb-2">
                Nome da Campanha *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Ex: Lançamento Produto X"
                className={`w-full px-3 py-2 bg-[#233648] border rounded-lg text-white placeholder-[#92adc9] focus:outline-none focus:ring-2 focus:ring-[#1172d4] ${
                  errors.name ? 'border-red-500' : 'border-[#324d67]'
                }`}
              />
              {errors.name && (
                <p className="text-red-400 text-sm mt-1">{errors.name}</p>
              )}
            </div>

            {/* Client */}
            <div className="mb-4">
              <label className="block text-white text-sm font-medium mb-2">
                Cliente
              </label>
              <input
                type="text"
                value={formData.client}
                onChange={(e) => handleInputChange('client', e.target.value)}
                placeholder="Nome do cliente"
                className="w-full px-3 py-2 bg-[#233648] border border-[#324d67] rounded-lg text-white placeholder-[#92adc9] focus:outline-none focus:ring-2 focus:ring-[#1172d4]"
              />
            </div>
          </div>

          {/* Objective */}
          <div>
            <label className="block text-white text-sm font-medium mb-2">
              Objetivo Principal *
            </label>
            <textarea
              value={formData.objective}
              onChange={(e) => handleInputChange('objective', e.target.value)}
              placeholder="Descreva o objetivo principal desta campanha..."
              rows={3}
              className={`w-full px-3 py-2 bg-[#233648] border rounded-lg text-white placeholder-[#92adc9] focus:outline-none focus:ring-2 focus:ring-[#1172d4] resize-none ${
                errors.objective ? 'border-red-500' : 'border-[#324d67]'
              }`}
            />
            {errors.objective && (
              <p className="text-red-400 text-sm mt-1">{errors.objective}</p>
            )}
          </div>

          {/* Target Audience */}
          <div>
            <label className="block text-white text-sm font-medium mb-2">
              Público-Alvo *
            </label>
            <textarea
              value={formData.targetAudience}
              onChange={(e) => handleInputChange('targetAudience', e.target.value)}
              placeholder="Descreva seu público-alvo..."
              rows={3}
              className={`w-full px-3 py-2 bg-[#233648] border rounded-lg text-white placeholder-[#92adc9] focus:outline-none focus:ring-2 focus:ring-[#1172d4] resize-none ${
                errors.targetAudience ? 'border-red-500' : 'border-[#324d67]'
              }`}
            />
            {errors.targetAudience && (
              <p className="text-red-400 text-sm mt-1">{errors.targetAudience}</p>
            )}
          </div>

          {/* Budget and Dates Row */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Budget */}
            <div>
              <label className="block text-white text-sm font-medium mb-2">
                Orçamento (R$)
              </label>
              <input
                type="number"
                value={formData.budget || ''}
                onChange={(e) => handleInputChange('budget', e.target.value ? Number(e.target.value) : '')}
                placeholder="50000"
                className="w-full px-3 py-2 bg-[#233648] border border-[#324d67] rounded-lg text-white placeholder-[#92adc9] focus:outline-none focus:ring-2 focus:ring-[#1172d4]"
              />
            </div>

            {/* Start Date */}
            <div>
              <label className="block text-white text-sm font-medium mb-2">
                Data de Início
              </label>
              <input
                type="date"
                value={formData.startDate}
                onChange={(e) => handleInputChange('startDate', e.target.value)}
                className="w-full px-3 py-2 bg-[#233648] border border-[#324d67] rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#1172d4]"
              />
            </div>

            {/* End Date */}
            <div>
              <label className="block text-white text-sm font-medium mb-2">
                Data de Fim
              </label>
              <input
                type="date"
                value={formData.endDate}
                onChange={(e) => handleInputChange('endDate', e.target.value)}
                className="w-full px-3 py-2 bg-[#233648] border border-[#324d67] rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#1172d4]"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t border-[#233648]">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <>
                  <svg className="w-4 h-4 animate-spin" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M232,128a104,104,0,0,1-208,0c0-41,23.81-78.36,60.66-95.27a8,8,0,0,1,6.68,14.54C60.15,61.59,40,93.27,40,128a88,88,0,0,0,176,0c0-34.73-20.15-66.41-51.34-80.73a8,8,0,0,1,6.68-14.54C208.19,49.64,232,87,232,128Z"/>
                  </svg>
                  Criando...
                </>
              ) : (
                'Criar e Continuar'
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateCampaignModal;
