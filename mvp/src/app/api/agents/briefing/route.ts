import { NextRequest, NextResponse } from 'next/server';
import { BriefingInputs, BriefingOutputs } from '@/types/campaign';

export async function POST(request: NextRequest) {
  try {
    const inputs: BriefingInputs = await request.json();
    
    // Validate required fields
    if (!inputs.description || inputs.description.trim().split(' ').length < 10) {
      return NextResponse.json(
        { success: false, error: 'Description must have at least 10 words' },
        { status: 400 }
      );
    }
    
    // Simulate agent processing time
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Mock web research and analysis
    const mockOutputs: BriefingOutputs = {
      pointsRaised: {
        doubts: [
          'Qual é o posicionamento atual da marca no mercado?',
          'Existem campanhas similares da concorrência?',
          'Qual é o histórico de performance de campanhas anteriores?',
          'Quais são as principais métricas de sucesso esperadas?'
        ],
        missingItems: [
          'Definição clara do tom de voz da marca',
          'Especificação de canais de distribuição prioritários',
          'Cronograma detalhado de execução',
          'Definição de KPIs específicos'
        ],
        criticalQuestions: [
          'Qual é o diferencial competitivo do produto/serviço?',
          'Como medir o ROI da campanha?',
          'Existem restrições criativas ou de marca?',
          'Qual é o prazo para execução da campanha?',
          'Há sazonalidades que devem ser consideradas?'
        ],
        opportunities: [
          'Mercado em crescimento de 15% ao ano no segmento',
          'Baixa saturação publicitária no nicho específico',
          'Tendência crescente de consumo digital',
          'Oportunidade de first-mover advantage'
        ],
        challenges: [
          'Concorrência estabelecida com alto share of voice',
          'Orçamento limitado comparado aos players principais',
          'Necessidade de educação do mercado sobre o produto',
          'Sazonalidade do setor pode impactar resultados'
        ]
      },
      briefingDraft: {
        title: `Briefing - ${inputs.campaignName || 'Nova Campanha'}`,
        htmlContent: `
          <div class="briefing-document">
            <h1>Briefing de Campanha</h1>
            
            <h2>1. Informações Gerais</h2>
            <p><strong>Cliente:</strong> ${inputs.client || 'A definir'}</p>
            <p><strong>Campanha:</strong> ${inputs.campaignName || 'Nova Campanha'}</p>
            <p><strong>Data:</strong> ${inputs.date || 'A definir'}</p>
            <p><strong>Orçamento:</strong> ${inputs.budget ? `R$ ${inputs.budget.toLocaleString()}` : 'A definir'}</p>
            
            <h2>2. Objetivo</h2>
            <p>${inputs.description}</p>
            
            <h2>3. Público-Alvo</h2>
            <p>${inputs.targetAudience || 'A definir com base na pesquisa'}</p>
            
            <h2>4. Direcionamento Criativo</h2>
            <p>${inputs.creativeDirection || 'A definir com base na estratégia'}</p>
            
            <h2>5. Análise de Mercado</h2>
            <p>Baseado na pesquisa realizada, identificamos oportunidades significativas no mercado-alvo. 
            O segmento apresenta crescimento consistente e há espaço para diferenciação através de 
            uma abordagem inovadora e centrada no usuário.</p>
            
            <h2>6. Estratégia Recomendada</h2>
            <p>Recomendamos uma abordagem multi-canal focada em:</p>
            <ul>
              <li>Canais digitais para alcance e engajamento</li>
              <li>Conteúdo educativo para awareness</li>
              <li>Experiências interativas para conversão</li>
              <li>Parcerias estratégicas para amplificação</li>
            </ul>
            
            <h2>7. Próximos Passos</h2>
            <p>1. Validação do briefing com stakeholders<br>
            2. Desenvolvimento do plano de campanha detalhado<br>
            3. Criação do plano de mídia<br>
            4. Desenvolvimento dos conceitos criativos</p>
          </div>
        `,
        sections: [
          {
            title: 'Informações Gerais',
            content: `Cliente: ${inputs.client || 'A definir'}`,
            isEditable: true
          },
          {
            title: 'Objetivo',
            content: inputs.description,
            isEditable: true
          },
          {
            title: 'Público-Alvo',
            content: inputs.targetAudience || 'A definir',
            isEditable: true
          }
        ]
      },
      webResearch: {
        clientInfo: `Pesquisa realizada sobre ${inputs.client || 'o cliente'} indica posicionamento sólido no mercado com oportunidades de crescimento.`,
        competitors: [
          'Concorrente A - Líder de mercado com 35% de share',
          'Concorrente B - Challenger com foco em inovação',
          'Concorrente C - Player regional com forte presença local'
        ],
        marketOpportunities: [
          'Crescimento do mercado digital em 25% ao ano',
          'Mudança de comportamento do consumidor pós-pandemia',
          'Oportunidade de diferenciação através da sustentabilidade'
        ],
        challenges: [
          'Alta competitividade no setor',
          'Necessidade de investimento em educação do mercado',
          'Sazonalidade pode impactar performance'
        ],
        criticalThemes: [
          'Transformação digital',
          'Sustentabilidade e responsabilidade social',
          'Experiência do cliente',
          'Personalização e dados'
        ]
      }
    };
    
    return NextResponse.json({
      success: true,
      data: mockOutputs,
      message: 'Briefing generated successfully'
    });
  } catch (error) {
    console.error('Error generating briefing:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate briefing' },
      { status: 500 }
    );
  }
}
