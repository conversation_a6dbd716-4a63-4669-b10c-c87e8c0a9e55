import { NextRequest, NextResponse } from 'next/server';
import { Campaign, CreateCampaignForm } from '@/types/campaign';

// Mock database - In production, this would be a real database
let campaigns: Campaign[] = [
  {
    id: '1',
    name: 'Lançamento FIAT IAT',
    client: 'FIAT',
    objective: 'Lançar novo modelo no mercado brasileiro',
    targetAudience: 'Jovens adultos de 25-40 anos',
    budget: 500000,
    status: 'in_progress',
    currentStage: 'briefing',
    stages: [],
    createdBy: 'user1',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: '2',
    name: 'Lançamento combo VivoAI',
    client: 'Vivo',
    objective: 'Promover novo plano de dados com IA',
    targetAudience: 'Profissionais de tecnologia',
    budget: 300000,
    status: 'in_progress',
    currentStage: 'briefing',
    stages: [],
    createdBy: 'user1',
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20')
  }
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'user1';
    
    // Filter campaigns by user
    const userCampaigns = campaigns.filter(campaign => campaign.createdBy === userId);
    
    return NextResponse.json({
      success: true,
      data: userCampaigns,
      message: 'Campaigns retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching campaigns:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch campaigns' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body: CreateCampaignForm = await request.json();
    
    // Validate required fields
    if (!body.name || !body.objective || !body.targetAudience) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Create new campaign
    const newCampaign: Campaign = {
      id: `campaign_${Date.now()}`,
      name: body.name,
      client: body.client,
      objective: body.objective,
      targetAudience: body.targetAudience,
      budget: body.budget,
      startDate: body.startDate ? new Date(body.startDate) : undefined,
      endDate: body.endDate ? new Date(body.endDate) : undefined,
      status: 'draft',
      currentStage: 'briefing',
      stages: [
        {
          stage: 'briefing',
          status: 'pending',
          documents: [],
          agentExecutions: []
        },
        {
          stage: 'planning',
          status: 'pending',
          documents: [],
          agentExecutions: []
        },
        {
          stage: 'media',
          status: 'pending',
          documents: [],
          agentExecutions: []
        },
        {
          stage: 'creative',
          status: 'pending',
          documents: [],
          agentExecutions: []
        }
      ],
      createdBy: 'user1', // In production, get from auth
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    campaigns.push(newCampaign);
    
    return NextResponse.json({
      success: true,
      data: newCampaign,
      message: 'Campaign created successfully'
    });
  } catch (error) {
    console.error('Error creating campaign:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create campaign' },
      { status: 500 }
    );
  }
}
