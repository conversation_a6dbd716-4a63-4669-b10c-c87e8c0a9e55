import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Container } from "@/components/ui/Container";
import { Check, Star, Zap } from "lucide-react";
import Link from "next/link";

const plans = [
    {
        name: "Starter",
        price: "R$ 97",
        period: "/mês",
        description: "Perfeito para freelancers e pequenas agências",
        features: [
            "5 campanhas simultâneas",
            "Acesso a 3 ferramentas principais",
            "Suporte por email",
            "Relatórios básicos",
            "1 usuário"
        ],
        popular: false,
        cta: "Come<PERSON><PERSON>"
    },
    {
        name: "Professional",
        price: "R$ 297",
        period: "/mês",
        description: "Ideal para agências em crescimento",
        features: [
            "20 campanhas simultâneas",
            "Acesso a todas as ferramentas",
            "Suporte prioritário",
            "Relatórios avançados",
            "Até 5 usuários",
            "Integrações ilimitadas",
            "Treinamento personalizado"
        ],
        popular: true,
        cta: "Começar Grátis"
    },
    {
        name: "Enterprise",
        price: "R$ 597",
        period: "/mês",
        description: "Para grandes agências e corporações",
        features: [
            "Campanhas ilimitadas",
            "Acesso a todas as ferramentas",
            "Suporte 24/7",
            "Relatórios customizados",
            "Usuários ilimitados",
            "Integrações personalizadas",
            "Consultoria estratégica",
            "SLA garantido"
        ],
        popular: false,
        cta: "Falar com Vendas"
    }
];

export default function PricingPage({ onBackToHome }: { onBackToHome: () => void }) {
    return (
        <div className="min-h-screen bg-background">
            <Container className="py-20">
                {/* Header */}
                <div className="text-center space-y-4 mb-16">
                    <Badge variant="secondary" className="w-fit mx-auto">
                        <Zap className="w-3 h-3 mr-2" />
                        Planos Flexíveis
                    </Badge>

                    <h1 className="text-4xl md:text-5xl lg:text-6xl tracking-tight">
                        Escolha o plano ideal para sua{" "}
                        <span className="text-primary">agência</span>
                    </h1>

                    <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                        Comece gratuitamente e escale conforme sua necessidade.
                        Todos os planos incluem acesso às ferramentas de IA especializadas.
                    </p>
                </div>

                {/* Pricing Cards */}
                <div className="grid md:grid-cols-3 gap-8 mb-16">
                    {plans.map((plan, index) => (
                        <Card
                            key={index}
                            className={`relative ${plan.popular ? 'border-primary shadow-lg scale-105' : ''}`}
                        >
                            {plan.popular && (
                                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                    <Badge className="bg-primary text-primary-foreground">
                                        <Star className="w-3 h-3 mr-1" />
                                        Mais Popular
                                    </Badge>
                                </div>
                            )}

                            <CardHeader className="text-center space-y-4">
                                <CardTitle className="text-2xl">{plan.name}</CardTitle>
                                <div className="space-y-2">
                                    <div className="text-4xl font-bold">
                                        {plan.price}
                                        <span className="text-lg font-normal text-muted-foreground">{plan.period}</span>
                                    </div>
                                    <p className="text-muted-foreground">{plan.description}</p>
                                </div>
                            </CardHeader>

                            <CardContent className="space-y-6">
                                <ul className="space-y-3">
                                    {plan.features.map((feature, featureIndex) => (
                                        <li key={featureIndex} className="flex items-center space-x-3">
                                            <Check className="w-5 h-5 text-green-500 shrink-0" />
                                            <span className="text-sm">{feature}</span>
                                        </li>
                                    ))}
                                </ul>

                                <Link href="/dashboard" className="block">
                                    <Button
                                        className="w-full"
                                        variant={plan.popular ? "default" : "outline"}
                                        size="lg"
                                    >
                                        {plan.cta}
                                    </Button>
                                </Link>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* FAQ Section */}
                <div className="text-center space-y-8">
                    <h2 className="text-3xl font-bold">Perguntas Frequentes</h2>

                    <div className="grid md:grid-cols-2 gap-8 text-left max-w-4xl mx-auto">
                        <div className="space-y-4">
                            <h3 className="font-semibold">Posso cancelar a qualquer momento?</h3>
                            <p className="text-muted-foreground">
                                Sim! Não há taxas de cancelamento e você pode cancelar sua assinatura a qualquer momento.
                            </p>
                        </div>

                        <div className="space-y-4">
                            <h3 className="font-semibold">Há período de teste gratuito?</h3>
                            <p className="text-muted-foreground">
                                Todos os planos incluem 14 dias gratuitos para você testar todas as funcionalidades.
                            </p>
                        </div>

                        <div className="space-y-4">
                            <h3 className="font-semibold">Posso mudar de plano depois?</h3>
                            <p className="text-muted-foreground">
                                Claro! Você pode fazer upgrade ou downgrade do seu plano a qualquer momento.
                            </p>
                        </div>

                        <div className="space-y-4">
                            <h3 className="font-semibold">Os dados ficam seguros?</h3>
                            <p className="text-muted-foreground">
                                Sim, utilizamos criptografia de ponta e seguimos as melhores práticas de segurança.
                            </p>
                        </div>
                    </div>
                </div>
            </Container>
        </div>
    );
}
