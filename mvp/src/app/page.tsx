"use client";
import { Footer } from '@/components/composite/Footer';
import { Header } from '@/components/composite/Header';
import { ToolsPage } from '@/components/screens/ToolsPage';
import { useState } from 'react';
import PricingPage from './preco/page';

import { BenefitsSection } from "@/components/composite/BenefitsSection";
import { CTASection } from "@/components/composite/CTASection";
import { FeaturesSection } from "@/components/composite/FeaturesSection";
import { Hero } from "@/components/composite/Hero";
import { ToolsCarousel } from "@/components/composite/ToolsCarousel";
import { ValueGrid } from "@/components/composite/ValueGrid";

import "./globals.css";

export default function HomePage() {

  const [currentPage, setCurrentPage] = useState<'home' | 'tools' | 'pricing'>('home');

  if (currentPage === 'tools') {
    return (
      <div className="dark min-h-screen bg-background">
        <ToolsPage onBackToHome={() => setCurrentPage('home')} />
      </div>
    );
  }

  if (currentPage === 'pricing') {
    return (
      <div className="dark min-h-screen bg-background">
        <PricingPage onBackToHome={() => setCurrentPage('home')} />
      </div>
    );
  }

  const handleNavigateToTools = () => {
    // Scroll to tools section or navigate to tools page
    const toolsSection = document.getElementById('ferramentas');
    if (toolsSection) {
      toolsSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleNavigateToPricing = () => {
    // Navigate to pricing page
    window.location.href = '/preco';
  };

  return (
    <>
      <div className="dark min-h-screen bg-background">
        <Header
          onNavigateToTools={() => setCurrentPage('tools')}
          onNavigateToPricing={() => setCurrentPage('pricing')}
        />
        <main>
          <Hero onNavigateToTools={handleNavigateToTools} />
          <FeaturesSection onNavigateToTools={handleNavigateToTools} />
          <BenefitsSection />
          <section id="ferramentas">
            <ValueGrid />
            <ToolsCarousel />
          </section>
          <CTASection onNavigateToPricing={handleNavigateToPricing} />
          <section id="testar" className="bg-background py-16">
            <div className="mx-auto w-full max-w-3xl px-4 text-center">
              <h2 className="text-2xl font-semibold text-foreground">Pronto para testar</h2>
              <p className="mt-2 text-muted-foreground">Crie uma campanha de exemplo e veja a mágica acontecer.</p>
              <a href="/dashboard" className="mt-6 inline-flex h-11 items-center justify-center rounded-xl bg-primary px-6 text-sm font-medium text-primary-foreground hover:bg-primary/90">Criar campanha</a>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </>
  );
}
