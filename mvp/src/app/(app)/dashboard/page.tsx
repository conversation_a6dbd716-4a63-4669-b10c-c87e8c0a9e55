"use client";

import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import {
  BarChart3,
  Brain,
  Calendar,
  ChevronDown,
  ChevronRight,
  Download,
  ExternalLink,
  FileText, Plus,
  Target, Users
} from "lucide-react";
import { useState } from "react";

export default function Dashboard() {
  const [campaignsExpanded, setCampaignsExpanded] = useState(true);



  // Mock data for campaigns
  const campaigns = [
    {
      id: 1,
      title: "Lançamento FIAT IAT",
      client: "FIAT",
      startDate: "03/09/2025",
      status: "Briefing",
      statusColor: "default" as const,
    },
    {
      id: 2,
      title: "Lançamento combo VivoAI",
      client: "Vivo",
      startDate: "03/09/2025",
      status: "Briefing",
      statusColor: "default" as const,
    },
    {
      id: 3,
      title: "Lançamento vivoai",
      client: "Vivo",
      startDate: "03/09/2025",
      status: "Briefing",
      statusColor: "default" as const,
    },
    {
      id: 4,
      title: "Lançamento App Fitness",
      client: "FitTracker Co.",
      startDate: "03/09/2025",
      status: "Estratégia",
      statusColor: "secondary" as const,
    },
    {
      id: 5,
      title: "Black Friday E-commerce",
      client: "TechStore Brasil",
      startDate: "03/09/2025",
      status: "Ativa",
      statusColor: "outline" as const,
    },
  ];

  // Mock data for tools
  const tools = [
    {
      id: 1,
      title: "Intake Radar",
      assetType: "Organiz...",
      icon: Target,
      bgColor: "bg-blue-500",
    },
    {
      id: 2,
      title: "Briefing Builder",
      assetType: "Crie bri...",
      icon: FileText,
      bgColor: "bg-green-500",
    },
    {
      id: 3,
      title: "Perguntas Críticas",
      assetType: "Identifi...",
      icon: Brain,
      bgColor: "bg-orange-500",
    },
    {
      id: 4,
      title: "Personas",
      assetType: "Desenv...",
      icon: Users,
      bgColor: "bg-purple-500",
    },
  ];

  return (
    <DashboardLayout>
      <div className="flex flex-col h-full">
        {/* Header Bar */}
        <div className="border-b bg-background px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-xl sm:text-2xl font-bold text-foreground">
                Bem-vindo, Thalles
              </h1>
              <p className="text-muted-foreground">Gerencie suas campanhas e acelere seus resultados</p>
            </div>
            <Button size="lg" className="gap-2 w-full sm:w-auto">
              <Plus className="h-4 w-4" />
              Nova Campanha
            </Button>
          </div>
        </div>

        {/* Central Content - Status Cards */}
        <div className="px-4 sm:px-6 lg:px-8 py-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
            <Card className="bg-card border border-border hover:shadow-md transition-shadow duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <div>
                  <CardTitle className="text-sm font-medium text-muted-foreground">Campanhas</CardTitle>
                  <CardTitle className="text-sm font-medium text-muted-foreground">Ativas</CardTitle>
                </div>
                <div className="p-2 bg-blue-500/10 rounded-lg">
                  <Target className="h-5 w-5 text-blue-500" />
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="text-3xl font-bold">6</div>
                <p className="text-xs text-green-500 flex items-center gap-1">
                  <span>↗</span> +2 esta semana
                </p>
              </CardContent>
            </Card>

            <Card className="bg-card border border-border hover:shadow-md transition-shadow duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <div>
                  <CardTitle className="text-sm font-medium text-muted-foreground">Agentes</CardTitle>
                  <CardTitle className="text-sm font-medium text-muted-foreground">Executados</CardTitle>
                </div>
                <div className="p-2 bg-green-500/10 rounded-lg">
                  <BarChart3 className="h-5 w-5 text-green-500" />
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="text-3xl font-bold">3</div>
                <p className="text-xs text-green-500 flex items-center gap-1">
                  <span>↗</span> 15 este mês
                </p>
              </CardContent>
            </Card>

            <Card className="bg-card border border-border hover:shadow-md transition-shadow duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <div>
                  <CardTitle className="text-sm font-medium text-muted-foreground">Templates</CardTitle>
                  <CardTitle className="text-sm font-medium text-muted-foreground">Salvos</CardTitle>
                </div>
                <div className="p-2 bg-purple-500/10 rounded-lg">
                  <FileText className="h-5 w-5 text-purple-500" />
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="text-3xl font-bold">12</div>
                <p className="text-xs text-green-500 flex items-center gap-1">
                  <span>↗</span> 3 novos
                </p>
              </CardContent>
            </Card>

            <Card className="bg-card border border-border hover:shadow-md transition-shadow duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <div>
                  <CardTitle className="text-sm font-medium text-muted-foreground">Última</CardTitle>
                  <CardTitle className="text-sm font-medium text-muted-foreground">Atualização</CardTitle>
                </div>
                <div className="p-2 bg-orange-500/10 rounded-lg">
                  <Calendar className="h-5 w-5 text-orange-500" />
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="text-3xl font-bold">03/09</div>
                <p className="text-xs text-muted-foreground">14:32</p>
              </CardContent>
            </Card>
          </div>

          {/* Two-Column Layout */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6">
            {/* Left Column - Campaign Management */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CardTitle>Suas campanhas</CardTitle>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => setCampaignsExpanded(!campaignsExpanded)}
                    >
                      {campaignsExpanded ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  {campaignsExpanded && (
                    <Button size="sm" variant="outline" className="gap-2">
                      <Plus className="h-4 w-4" />
                      Add Campaign
                    </Button>
                  )}
                </div>
              </CardHeader>
              {campaignsExpanded && (
                <CardContent>
                  <div className="space-y-4">
                    {campaigns.map((campaign) => (
                      <div
                        key={campaign.id}
                        className="flex flex-col space-y-3 p-4 border rounded-lg hover:bg-accent/50 transition-colors"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium text-sm">{campaign.title}</h4>
                            <div className="flex items-center gap-2 mt-1">
                              <Users className="h-3 w-3 text-muted-foreground" />
                              <span className="text-xs text-muted-foreground">{campaign.client}</span>
                              <Calendar className="h-3 w-3 text-muted-foreground ml-2" />
                              <span className="text-xs text-muted-foreground">{campaign.startDate}</span>
                            </div>
                          </div>
                          <Badge
                            variant={campaign.status === "Briefing" ? "default" : campaign.status === "Estratégia" ? "secondary" : "outline"}
                            className={`text-xs ${campaign.status === "Briefing" ? "bg-blue-500 text-white" :
                              campaign.status === "Estratégia" ? "bg-purple-500 text-white" :
                                "bg-green-500 text-white"
                              }`}
                          >
                            {campaign.status}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button size="sm" variant="outline" className="h-7 text-xs gap-1 flex-shrink-0">
                            <ExternalLink className="h-3 w-3" />
                            Abrir
                          </Button>
                          <Button size="sm" variant="outline" className="h-7 text-xs gap-1 flex-shrink-0">
                            <Download className="h-3 w-3" />
                            Exportar
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Right Column - Tools Section */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <div className="p-1 bg-green-500/10 rounded">
                    <Target className="h-4 w-4 text-green-500" />
                  </div>
                  <CardTitle>Agentes Rápidos</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {tools.map((tool) => (
                    <div
                      key={tool.id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg ${tool.bgColor}`}>
                          <tool.icon className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <p className="font-medium text-sm">{tool.title}</p>
                          <p className="text-xs text-muted-foreground">{tool.assetType}</p>
                        </div>
                      </div>
                      <Button size="sm" variant="outline" className="h-7 text-xs gap-1">
                        <ExternalLink className="h-3 w-3" />
                        <span className="hidden sm:inline">Abrir</span>
                      </Button>
                    </div>
                  ))}
                </div>
                <div className="mt-4 pt-4 border-t">
                  <Button variant="outline" className="w-full gap-2">
                    <BarChart3 className="h-4 w-4" />
                    Ver todos os agentes
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
