"use client";

import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import {
  BarChart3,
  Brain,
  Calendar,
  ChevronDown,
  ChevronRight,
  Download,
  ExternalLink,
  FileText,
  Image,
  PenTool,
  Plus,
  Target,
  Trash2,
  Video
} from "lucide-react";
import { useState } from "react";

export default function Dashboard() {
  const [campaignsExpanded, setCampaignsExpanded] = useState(true);

  // Mock user data
  const user = {
    name: "<PERSON>",
  };

  // Mock data for campaigns
  const campaigns = [
    {
      id: 1,
      title: "Campanha Black Friday 2024",
      client: "Loja Virtual ABC",
      startDate: "15 Nov 2024",
      status: "Ativa",
      statusColor: "default" as const,
    },
    {
      id: 2,
      title: "Lançamento Produto X",
      client: "Tech Solutions",
      startDate: "01 Nov 2024",
      status: "Pausada",
      statusColor: "secondary" as const,
    },
    {
      id: 3,
      title: "Brand Awareness Q4",
      client: "Startup Inovadora",
      startDate: "20 Out 2024",
      status: "Concluída",
      statusColor: "outline" as const,
    },
  ];

  // Mock data for tools
  const tools = [
    {
      id: 1,
      title: "Briefing Inicial",
      assetType: "Documento",
      icon: FileText,
    },
    {
      id: 2,
      title: "Conceito Criativo",
      assetType: "PDF",
      icon: Brain,
    },
    {
      id: 3,
      title: "Gerador de Imagens",
      assetType: "Imagem",
      icon: Image,
    },
    {
      id: 4,
      title: "Roteiro de Vídeo",
      assetType: "Documento",
      icon: Video,
    },
    {
      id: 5,
      title: "Copy para Anúncios",
      assetType: "Texto",
      icon: PenTool,
    },
  ];

  return (
    <DashboardLayout>
      <div className="flex flex-col h-full">
        {/* Header Bar */}
        <div className="border-b bg-background px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-xl sm:text-2xl font-bold text-foreground">
                Bem-vindo, {user.name}!
              </h1>
              <p className="text-muted-foreground">Gerencie suas campanhas</p>
            </div>
            <Button size="lg" className="gap-2 w-full sm:w-auto">
              <Plus className="h-4 w-4" />
              Add Campaign
            </Button>
          </div>
        </div>

        {/* Central Content - Status Cards */}
        <div className="px-4 sm:px-6 lg:px-8 py-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
            <Card className="hover:shadow-md transition-shadow duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Campanhas Ativas</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">12</div>
                <p className="text-xs text-muted-foreground">+2 desde ontem</p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Agentes Executados</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">247</div>
                <p className="text-xs text-muted-foreground">Esta semana</p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow duration-200 sm:col-span-2 lg:col-span-1">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Última Atualização</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Hoje</div>
                <p className="text-xs text-muted-foreground">14:32</p>
              </CardContent>
            </Card>
          </div>

          {/* Two-Column Layout */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6">
            {/* Left Column - Campaign Management */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CardTitle>Suas campanhas</CardTitle>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => setCampaignsExpanded(!campaignsExpanded)}
                    >
                      {campaignsExpanded ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  {campaignsExpanded && (
                    <Button size="sm" variant="outline" className="gap-2">
                      <Plus className="h-4 w-4" />
                      Add Campaign
                    </Button>
                  )}
                </div>
              </CardHeader>
              {campaignsExpanded && (
                <CardContent>
                  <div className="space-y-4">
                    {campaigns.map((campaign) => (
                      <div
                        key={campaign.id}
                        className="flex flex-col space-y-2 p-4 border rounded-lg hover:bg-accent/50 transition-colors"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium text-sm">{campaign.title}</h4>
                            <p className="text-xs text-muted-foreground">
                              {campaign.client} • {campaign.startDate}
                            </p>
                          </div>
                          <Badge variant={campaign.statusColor} className="text-xs">
                            {campaign.status}
                          </Badge>
                        </div>
                        <div className="flex flex-wrap items-center gap-2">
                          <Button size="sm" variant="outline" className="h-7 text-xs gap-1 flex-shrink-0">
                            <ExternalLink className="h-3 w-3" />
                            <span className="hidden sm:inline">Abrir</span>
                          </Button>
                          <Button size="sm" variant="outline" className="h-7 text-xs gap-1 flex-shrink-0">
                            <Download className="h-3 w-3" />
                            <span className="hidden sm:inline">Exportar</span>
                          </Button>
                          <Button size="sm" variant="outline" className="h-7 text-xs gap-1 text-destructive hover:text-destructive flex-shrink-0">
                            <Trash2 className="h-3 w-3" />
                            <span className="hidden sm:inline">Excluir</span>
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Right Column - Tools Section */}
            <Card>
              <CardHeader>
                <CardTitle>Ferramentas</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {tools.map((tool) => (
                    <div
                      key={tool.id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-muted">
                          <tool.icon className="h-4 w-4" />
                        </div>
                        <div>
                          <p className="font-medium text-sm">{tool.title}</p>
                          <p className="text-xs text-muted-foreground">{tool.assetType}</p>
                        </div>
                      </div>
                      <Button size="sm" variant="outline" className="h-7 text-xs gap-1">
                        <ExternalLink className="h-3 w-3" />
                        Abrir
                      </Button>
                    </div>
                  ))}
                </div>
                <div className="mt-4 pt-4 border-t">
                  <Button variant="outline" className="w-full gap-2">
                    <BarChart3 className="h-4 w-4" />
                    Ver todos os agentes
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
