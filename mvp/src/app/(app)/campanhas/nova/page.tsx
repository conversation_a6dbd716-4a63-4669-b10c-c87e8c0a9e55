'use client';

import React, { useState } from 'react';
import { CampaignStage } from '@/types/campaign';
import CampaignStageLayout from '@/components/campaign/CampaignStageLayout';
import BriefingStage from '@/components/campaign/stages/BriefingStage';

export default function NewCampaignPage() {
  const [currentStage, setCurrentStage] = useState<CampaignStage>('briefing');
  const [campaignData, setCampaignData] = useState({
    id: 'temp-id',
    name: 'Nova Campanha',
    client: '',
    objective: '',
    targetAudience: '',
    budget: 0,
    stages: []
  });

  const stages = [
    { id: 'briefing', name: 'Atendimento', description: 'Criação do briefing', status: 'in_progress' as const },
    { id: 'planning', name: 'Planejamento', description: 'Plano de campanha', status: 'pending' as const },
    { id: 'media', name: '<PERSON>ídia', description: 'Plano de mídia', status: 'pending' as const },
    { id: 'creative', name: '<PERSON><PERSON><PERSON>', description: 'Direcional criativo', status: 'pending' as const },
    { id: 'finalization', name: 'Finalização', description: 'Revisão e entrega', status: 'pending' as const }
  ];

  const handleStageComplete = (stage: CampaignStage, data: any) => {
    console.log(`Stage ${stage} completed with data:`, data);
    
    // Update stage status and move to next
    const currentIndex = stages.findIndex(s => s.id === stage);
    if (currentIndex < stages.length - 1) {
      const nextStage = stages[currentIndex + 1].id as CampaignStage;
      setCurrentStage(nextStage);
    }
  };

  const renderCurrentStage = () => {
    switch (currentStage) {
      case 'briefing':
        return (
          <BriefingStage
            campaignData={campaignData}
            onComplete={(data) => handleStageComplete('briefing', data)}
            onBack={() => window.history.back()}
          />
        );
      case 'planning':
        return <div className="text-white">Planejamento Stage - Coming Soon</div>;
      case 'media':
        return <div className="text-white">Media Stage - Coming Soon</div>;
      case 'creative':
        return <div className="text-white">Creative Stage - Coming Soon</div>;
      case 'finalization':
        return <div className="text-white">Finalization Stage - Coming Soon</div>;
      default:
        return <div className="text-white">Unknown Stage</div>;
    }
  };

  return (
    <CampaignStageLayout
      campaignName={campaignData.name}
      stages={stages}
      currentStage={currentStage}
      onStageClick={setCurrentStage}
    >
      {renderCurrentStage()}
    </CampaignStageLayout>
  );
}
