export interface Campaign {
  id: string;
  name: string;
  client?: string;
  objective: string;
  targetAudience: string;
  budget?: number;
  startDate?: Date;
  endDate?: Date;
  status: 'draft' | 'in_progress' | 'completed' | 'cancelled';
  currentStage: CampaignStage;
  stages: CampaignStageData[];
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export type CampaignStage = 'briefing' | 'planning' | 'media' | 'creative' | 'finalization';

export interface CampaignStageData {
  stage: CampaignStage;
  status: 'pending' | 'in_progress' | 'completed' | 'skipped';
  startedAt?: Date;
  completedAt?: Date;
  documents: Document[];
  agentExecutions: AgentExecution[];
}

export interface Document {
  id: string;
  name: string;
  type: DocumentType;
  content: string;
  htmlContent?: string;
  stage: CampaignStage;
  campaignId: string;
  version: number;
  isEditable: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  editHistory: DocumentEdit[];
}

export type DocumentType = 
  | 'briefing_draft'
  | 'briefing_questions'
  | 'briefing_final'
  | 'campaign_plan'
  | 'media_plan'
  | 'creative_concept'
  | 'key_visual'
  | 'presentation'
  | 'pdf_export';

export interface DocumentEdit {
  id: string;
  documentId: string;
  content: string;
  htmlContent?: string;
  editedBy: string;
  editedAt: Date;
  changeDescription?: string;
}

export interface AgentExecution {
  id: string;
  agentType: AgentType;
  stage: CampaignStage;
  campaignId: string;
  inputs: Record<string, any>;
  outputs: AgentOutput[];
  status: 'pending' | 'running' | 'completed' | 'failed';
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  executedBy: string;
}

export type AgentType = 
  | 'briefing_creator'
  | 'campaign_planner'
  | 'media_planner'
  | 'creative_director';

export interface AgentOutput {
  type: 'document' | 'analysis' | 'questions' | 'recommendations';
  title: string;
  content: string;
  htmlContent?: string;
  metadata?: Record<string, any>;
}

// Form types for campaign creation
export interface CreateCampaignForm {
  name: string;
  client?: string;
  objective: string;
  targetAudience: string;
  budget?: number;
  startDate?: string;
  endDate?: string;
}

// Briefing stage specific types
export interface BriefingInputs {
  campaignName?: string;
  client?: string;
  date?: string;
  budget?: number;
  targetAudience?: string;
  creativeDirection?: string;
  description: string; // minimum 10 words required
}

export interface BriefingOutputs {
  pointsRaised: {
    doubts: string[];
    missingItems: string[];
    criticalQuestions: string[];
    opportunities: string[];
    challenges: string[];
  };
  briefingDraft: {
    title: string;
    htmlContent: string;
    sections: BriefingSection[];
  };
  webResearch: {
    clientInfo: string;
    competitors: string[];
    marketOpportunities: string[];
    challenges: string[];
    criticalThemes: string[];
  };
}

export interface BriefingSection {
  title: string;
  content: string;
  isEditable: boolean;
}

// API Response types
export interface CampaignResponse {
  campaign: Campaign;
  message?: string;
}

export interface AgentExecutionResponse {
  execution: AgentExecution;
  documents: Document[];
  message?: string;
}
