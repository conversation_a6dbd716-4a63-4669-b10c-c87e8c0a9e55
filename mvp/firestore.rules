rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection - users can only read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Future collections can be added here
    // Example: Campaigns collection
    // match /campaigns/{campaignId} {
    //   allow read, write: if request.auth != null;
    // }
    
    // Default rule - deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
