import React from 'react';

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description }) => {
  return (
    <div className="flex flex-1 gap-3 rounded-lg border border-[#324d67] bg-[#192633] p-4 flex-col">
      <div className="text-white">
        {icon}
      </div>
      <div className="flex flex-col gap-1">
        <h2 className="text-white text-base font-bold leading-tight">
          {title}
        </h2>
        <p className="text-[#92adc9] text-sm font-normal leading-normal">
          {description}
        </p>
      </div>
    </div>
  );
};

export default FeatureCard;
