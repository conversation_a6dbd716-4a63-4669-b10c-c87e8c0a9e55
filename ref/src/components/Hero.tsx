import React from 'react';
import Button from './ui/Button';

const Hero: React.FC = () => {
  return (
    <div className="@container">
      <div className="@[480px]:p-4">
        <div 
          className="flex min-h-[480px] flex-col gap-6 bg-cover bg-center bg-no-repeat @[480px]:gap-8 @[480px]:rounded-lg items-center justify-center p-4"
          style={{
            backgroundImage: 'linear-gradient(rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%), url("https://lh3.googleusercontent.com/aida-public/AB6AXuAGZV4fH_gE-JdmnZFatFs_bPddKomUr33S7O_9L4Ux6TmBJxl0FWsUicHWHuqOa5chLIvdV9oxFTReOuzVAHCyhFygAAPq-aGHp9c5rlINIiFgMumuuDxpCrtYZhB0gyUi0x7pVR6mQgOoqz1wVLpTYmGkcV20pAsrzA7Jg5nXJKeRSP2KGwjgcv_xLV_rfy74ViU62-tYhUTGfb-E0DBD_MdbtieGgWlKYrhLEl6HzTmw9lb-nD-l2mdBKzW6rzqXCBHMRnk4rvM")'
          }}
        >
          <div className="flex flex-col gap-2 text-center">
            <h1 className="text-white text-4xl font-black leading-tight tracking-[-0.033em] @[480px]:text-5xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em]">
              Potencialize sua Agência com uTulz
            </h1>
            <h2 className="text-white text-sm font-normal leading-normal @[480px]:text-base @[480px]:font-normal @[480px]:leading-normal">
              A plataforma definitiva para agências de publicidade, automatizando tarefas,
              gerando conteúdo inteligente e otimizando campanhas.
            </h2>
          </div>
          <Button 
            size="lg"
            className="@[480px]:h-12 @[480px]:px-5 @[480px]:text-base"
          >
            Criar Conta
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Hero;
