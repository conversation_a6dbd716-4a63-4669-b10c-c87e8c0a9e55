'use client';

import Link from 'next/link';
import React, { useState } from 'react';

interface SidebarProps {
  onClose?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ onClose }) => {
  const [showUserDropdown, setShowUserDropdown] = useState(false);

  const navigationItems = [
    { name: 'Dashboard', href: '/dashboard', active: true },
    { name: 'Clientes', href: '/clientes', active: false },
    { name: 'Camp<PERSON><PERSON>', href: '/campanhas', active: false },
    { name: 'Ferramentas', href: '/ferramentas', active: false },
    { name: 'Templates', href: '/templates', active: false },
    { name: 'Integraçõ<PERSON>', href: '/integracoes', active: false },
  ];

  return (
    <div className="w-64 lg:w-72 bg-[#0f1419] border-r border-[#233648] flex flex-col h-full">
      {/* Header Section */}
      <div className="p-6 border-b border-[#233648]">
        {/* Logo and Agency */}
        <div className="flex items-center gap-3 mb-4">
          <div className="size-8">
            <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clipPath="url(#clip0_6_330)">
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M24 0.757355L47.2426 24L24 47.2426L0.757355 24L24 0.757355ZM21 35.7574V12.2426L9.24264 24L21 35.7574Z"
                  fill="currentColor"
                />
              </g>
              <defs>
                <clipPath id="clip0_6_330">
                  <rect width="48" height="48" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </div>
          <div>
            <h1 className="text-white text-lg font-bold">uTulz</h1>
          </div>
        </div>

        {/* Agency and User Info */}
        <div className="text-sm">
          <p className="text-[#92adc9] mb-1">Suas ferramentas para</p>
          <p className="text-[#92adc9] mb-3">campanhas</p>
          <p className="text-white font-medium">Thalles Freitas</p>
          <p className="text-[#92adc9] text-xs">Diretor Criativo</p>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {navigationItems.map((item) => (
            <li key={item.name}>
              <Link
                href={item.href}
                className={`flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${item.active
                  ? 'bg-[#1172d4] text-white'
                  : 'text-[#92adc9] hover:bg-[#233648] hover:text-white'
                  }`}
              >
                {/* Icons for each menu item */}
                {item.name === 'Dashboard' && (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M104,40H56A16,16,0,0,0,40,56v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V56A16,16,0,0,0,104,40Zm0,64H56V56h48v48Zm96-64H152a16,16,0,0,0-16,16v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V56A16,16,0,0,0,200,40Zm0,64H152V56h48v48ZM104,136H56a16,16,0,0,0-16,16v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V152A16,16,0,0,0,104,136Zm0,64H56V152h48v48Zm96-64H152a16,16,0,0,0-16,16v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V152A16,16,0,0,0,200,136Zm0,64H152V152h48v48Z" />
                  </svg>
                )}
                {item.name === 'Clientes' && (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M117.25,157.92a60,60,0,1,0-66.5,0A95.83,95.83,0,0,0,3.53,195.63a8,8,0,1,0,13.4,8.74,80,80,0,0,1,134.14,0,8,8,0,0,0,13.4-8.74A95.83,95.83,0,0,0,117.25,157.92ZM40,108a44,44,0,1,1,44,44A44.05,44.05,0,0,1,40,108Zm210.14,98.7a8,8,0,0,1-11.07-2.33A79.83,79.83,0,0,0,172,168a8,8,0,0,1,0-16,44,44,0,1,0-16.34-84.87,8,8,0,1,1-5.94-14.85,60,60,0,0,1,55.53,105.64,95.83,95.83,0,0,1,47.22,37.71A8,8,0,0,1,250.14,206.7Z" />
                  </svg>
                )}
                {item.name === 'Campanhas' && (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M228.54,86.66l-176.06-54A16,16,0,0,0,32,48V192a16,16,0,0,0,16,16,16,16,0,0,0,4.52-.65L76,201.05a16,16,0,0,0,10.47-15.05V176h12.85L143.64,182a16,16,0,0,0,4.36.6,16,16,0,0,0,16-16V89.35a16,16,0,0,0-12.36-15.69ZM76,185.05,48,192V48l28,8.61Zm72-18.7L92,160V80l56,16.65ZM208,112v32a8,8,0,0,1-16,0V112a8,8,0,0,1,16,0Zm32,0v32a8,8,0,0,1-16,0V112a8,8,0,0,1,16,0Z" />
                  </svg>
                )}
                {item.name === 'Ferramentas' && (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M227.31,73.37,182.63,28.69a16,16,0,0,0-22.63,0L36.69,152A15.86,15.86,0,0,0,32,163.31V208a16,16,0,0,0,16,16H92.69A15.86,15.86,0,0,0,104,219.31L227.31,96a16,16,0,0,0,0-22.63ZM92.69,208H48V163.31l88-88L180.69,120ZM192,108.69,147.31,64l24-24L216,84.69Z" />
                  </svg>
                )}
                {item.name === 'Templates' && (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M200,32H163.74a47.92,47.92,0,0,0-71.48,0H56A16,16,0,0,0,40,48V216a16,16,0,0,0,16,16H200a16,16,0,0,0,16-16V48A16,16,0,0,0,200,32Zm-72,0a32,32,0,0,1,32,32H96A32,32,0,0,1,128,32Zm72,184H56V48H72V64a8,8,0,0,0,8,8h96a8,8,0,0,0,8-8V48h16Z" />
                  </svg>
                )}
                {item.name === 'Integrações' && (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M240,112H224V96a16,16,0,0,0-16-16H192V64a16,16,0,0,0-16-16H80A16,16,0,0,0,64,64V80H48A16,16,0,0,0,32,96v16H16a8,8,0,0,0,0,16H32v16a16,16,0,0,0,16,16H64v16a16,16,0,0,0,16,16h96a16,16,0,0,0,16-16V160h16a16,16,0,0,0,16-16V128h16a8,8,0,0,0,0-16ZM192,144H176a8,8,0,0,0-8,8v16H80V152a8,8,0,0,0-8-8H48V96H72a8,8,0,0,0,8-8V64h96V88a8,8,0,0,0,8,8h24Z" />
                  </svg>
                )}
                {item.name}
              </Link>
            </li>
          ))}
        </ul>
      </nav>

      {/* User Profile Footer */}
      <div className="p-4 border-t border-[#233648]">
        <div className="relative">
          <button
            onClick={() => setShowUserDropdown(!showUserDropdown)}
            className="flex items-center gap-3 w-full p-3 rounded-lg hover:bg-[#233648] transition-colors"
          >
            {/* User Avatar */}
            <div className="w-8 h-8 bg-[#1172d4] rounded-full flex items-center justify-center text-sm font-bold">
              TF
            </div>
            <div className="flex-1 text-left">
              <p className="text-white text-sm font-medium">Thalles Freitas</p>
            </div>
            <svg
              className={`w-4 h-4 text-[#92adc9] transition-transform ${showUserDropdown ? 'rotate-180' : ''}`}
              fill="currentColor"
              viewBox="0 0 256 256"
            >
              <path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z" />
            </svg>
          </button>

          {/* Dropdown Menu */}
          {showUserDropdown && (
            <div className="absolute bottom-full left-0 right-0 mb-2 bg-[#233648] rounded-lg border border-[#324d67] shadow-lg">
              <button className="w-full px-4 py-3 text-left text-sm text-white hover:bg-[#324d67] rounded-lg transition-colors">
                Sign Out
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
