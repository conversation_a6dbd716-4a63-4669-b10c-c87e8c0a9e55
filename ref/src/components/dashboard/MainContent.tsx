'use client';

import Button from '@/components/ui/Button';
import React from 'react';
import CampaignsSection from './CampaignsSection';
import DashboardCards from './DashboardCards';
import ToolsSection from './ToolsSection';

interface MainContentProps {
  onMenuClick?: () => void;
}

const MainContent: React.FC<MainContentProps> = ({ onMenuClick }) => {
  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* Header */}
      <div className="p-4 lg:p-6 border-b border-[#233648]">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Mobile Menu Button */}
            <button
              onClick={onMenuClick}
              className="lg:hidden p-2 text-[#92adc9] hover:text-white hover:bg-[#233648] rounded transition-colors"
            >
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 256 256">
                <path d="M224,128a8,8,0,0,1-8,8H40a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128ZM40,72H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16ZM216,184H40a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16Z" />
              </svg>
            </button>

            <div>
              <h1 className="text-white text-xl lg:text-2xl font-bold mb-1">
                Bem-vindo, Thalles
              </h1>
              <p className="text-[#92adc9] text-sm">
                Gerencie suas campanhas e acelere seus resultados
              </p>
            </div>
          </div>

          <Button variant="primary" className="flex items-center gap-2 text-sm lg:text-base">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 256 256">
              <path d="M224,128a8,8,0,0,1-8,8H136v80a8,8,0,0,1-16,0V136H40a8,8,0,0,1,0-16h80V40a8,8,0,0,1,16,0v80h80A8,8,0,0,1,224,128Z" />
            </svg>
            <span className="hidden sm:inline">Nova Campanha</span>
            <span className="sm:hidden">Nova</span>
          </Button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 overflow-auto">
        <div className="p-4 lg:p-6">
          {/* Dashboard Cards */}
          <DashboardCards />

          {/* Content Grid */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 lg:gap-6 mt-4 lg:mt-6">
            {/* Campaigns Section - Takes 2 columns on xl screens */}
            <div className="xl:col-span-2">
              <CampaignsSection />
            </div>

            {/* Tools Section - Takes 1 column on xl screens */}
            <div className="xl:col-span-1">
              <ToolsSection />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MainContent;
