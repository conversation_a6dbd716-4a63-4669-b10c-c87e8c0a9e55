'use client';

import Button from '@/components/ui/Button';
import React, { useState } from 'react';

interface Campaign {
  id: string;
  title: string;
  client: string;
  startDate: string;
  status: 'Briefing' | 'Estratégia' | 'Ativa';
  statusColor: string;
}

const CampaignsSection: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(true);

  const campaigns: Campaign[] = [
    {
      id: '1',
      title: 'Lançamento FIAT IAT',
      client: 'FIAT',
      startDate: '03/09/2025',
      status: 'Briefing',
      statusColor: 'bg-blue-500',
    },
    {
      id: '2',
      title: 'Lançamento combo VivoAI',
      client: 'Vivo',
      startDate: '03/09/2025',
      status: 'Briefing',
      statusColor: 'bg-blue-500',
    },
    {
      id: '3',
      title: 'Lançamento vivoai',
      client: 'Vivo',
      startDate: '03/09/2025',
      status: 'Briefing',
      statusColor: 'bg-blue-500',
    },
    {
      id: '4',
      title: 'Lançamento App Fitness',
      client: 'FitTracker Co.',
      startDate: '03/09/2025',
      status: 'Estratégia',
      statusColor: 'bg-purple-500',
    },
    {
      id: '5',
      title: 'Black Friday E-commerce',
      client: 'TechStore Brasil',
      startDate: '03/09/2025',
      status: 'Ativa',
      statusColor: 'bg-green-500',
    },
  ];

  return (
    <div className="bg-[#1a2332] border border-[#233648] rounded-lg">
      {/* Header */}
      <div className="p-6 border-b border-[#233648]">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <h2 className="text-white text-lg font-semibold">Suas Campanhas</h2>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-[#92adc9] hover:text-white transition-colors"
            >
              <svg
                className={`w-5 h-5 transition-transform ${isExpanded ? 'rotate-90' : ''}`}
                fill="currentColor"
                viewBox="0 0 256 256"
              >
                <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z" />
              </svg>
            </button>
          </div>
          <Button variant="primary" size="sm" className="flex items-center gap-2">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 256 256">
              <path d="M224,128a8,8,0,0,1-8,8H136v80a8,8,0,0,1-16,0V136H40a8,8,0,0,1,0-16h80V40a8,8,0,0,1,16,0v80h80A8,8,0,0,1,224,128Z" />
            </svg>
            Nova
          </Button>
        </div>
      </div>

      {/* Campaign List */}
      {isExpanded && (
        <div className="p-6 space-y-4">
          {campaigns.map((campaign) => (
            <div
              key={campaign.id}
              className="bg-[#233648] border border-[#324d67] rounded-lg p-4 hover:bg-[#2a4155] transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-white font-medium">{campaign.title}</h3>
                    <span className={`px-2 py-1 rounded text-xs font-medium text-white ${campaign.statusColor}`}>
                      {campaign.status}
                    </span>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-[#92adc9]">
                    <div className="flex items-center gap-1">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 256 256">
                        <path d="M117.25,157.92a60,60,0,1,0-66.5,0A95.83,95.83,0,0,0,3.53,195.63a8,8,0,1,0,13.4,8.74,80,80,0,0,1,134.14,0,8,8,0,0,0,13.4-8.74A95.83,95.83,0,0,0,117.25,157.92ZM40,108a44,44,0,1,1,44,44A44.05,44.05,0,0,1,40,108Zm210.14,98.7a8,8,0,0,1-11.07-2.33A79.83,79.83,0,0,0,172,168a8,8,0,0,1,0-16,44,44,0,1,0-16.34-84.87,8,8,0,1,1-5.94-14.85,60,60,0,0,1,55.53,105.64,95.83,95.83,0,0,1,47.22,37.71A8,8,0,0,1,250.14,206.7Z" />
                      </svg>
                      {campaign.client}
                    </div>
                    <div className="flex items-center gap-1">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 256 256">
                        <path d="M208,32H184V24a8,8,0,0,0-16,0v8H88V24a8,8,0,0,0-16,0v8H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32ZM72,48v8a8,8,0,0,0,16,0V48h80v8a8,8,0,0,0,16,0V48h24V80H48V48ZM208,208H48V96H208V208Z" />
                      </svg>
                      {campaign.startDate}
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center gap-2">
                  <button
                    className="p-2 text-[#92adc9] hover:text-white hover:bg-[#324d67] rounded transition-colors"
                    title="Abrir campanha"
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 256 256">
                      <path d="M247.31,124.76c-.35-.79-8.82-19.58-27.65-38.41C194.57,61.26,162.88,48,128,48S61.43,61.26,36.34,86.35C17.51,105.18,9,124,8.69,124.76a8,8,0,0,0,0,6.5c.35.79,8.82,19.57,27.65,38.4C61.43,194.74,93.12,208,128,208s66.57-13.26,91.66-38.34c18.83-18.83,27.3-37.61,27.65-38.4A8,8,0,0,0,247.31,124.76ZM128,192c-30.78,0-57.67-11.19-79.93-33.25A133.47,133.47,0,0,1,25,128,133.33,133.33,0,0,1,48.07,97.25C70.33,75.19,97.22,64,128,64s57.67,11.19,79.93,33.25A133.46,133.46,0,0,1,231.05,128C223.84,141.46,192.43,192,128,192Zm0-112a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160Z" />
                    </svg>
                  </button>
                  <span className="text-[#92adc9] text-sm">Abrir</span>

                  <button
                    className="p-2 text-[#92adc9] hover:text-white hover:bg-[#324d67] rounded transition-colors"
                    title="Exportar campanha"
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 256 256">
                      <path d="M224,152a8,8,0,0,1-8,8H192v24a8,8,0,0,1-16,0V160H152a8,8,0,0,1,0-16h24V120a8,8,0,0,1,16,0v24h24A8,8,0,0,1,224,152ZM112,160H40a8,8,0,0,1,0-16h72a8,8,0,0,1,0,16Zm0-48H40a8,8,0,0,1,0-16h72a8,8,0,0,1,0,16Zm0-48H40a8,8,0,0,1,0-16h72a8,8,0,0,1,0,16Z" />
                    </svg>
                  </button>
                  <span className="text-[#92adc9] text-sm">Exportar</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CampaignsSection;
