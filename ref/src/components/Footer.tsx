import Link from 'next/link';
import React from 'react';
import { FacebookIcon, InstagramIcon, TwitterIcon } from './Icons';

const Footer: React.FC = () => {
  return (
    <footer className="flex justify-center">
      <div className="flex max-w-[960px] flex-1 flex-col">
        <footer className="flex flex-col gap-6 px-5 py-10 text-center @container">
          <div className="flex flex-wrap items-center justify-center gap-6 @[480px]:flex-row @[480px]:justify-around">
            <Link
              href="/sobre"
              className="text-[#92adc9] text-base font-normal leading-normal min-w-40 hover:text-white transition-colors"
            >
              Sobre nós
            </Link>
            <Link
              href="/contato"
              className="text-[#92adc9] text-base font-normal leading-normal min-w-40 hover:text-white transition-colors"
            >
              Contato
            </Link>
            <Link
              href="/termos"
              className="text-[#92adc9] text-base font-normal leading-normal min-w-40 hover:text-white transition-colors"
            >
              Termos de Serviço
            </Link>
            <Link
              href="/privacidade"
              className="text-[#92adc9] text-base font-normal leading-normal min-w-40 hover:text-white transition-colors"
            >
              Política de Privacidade
            </Link>
          </div>

          <div className="flex flex-wrap justify-center gap-4">
            <Link href="#" className="text-[#92adc9] hover:text-white transition-colors">
              <TwitterIcon size={24} />
            </Link>
            <Link href="#" className="text-[#92adc9] hover:text-white transition-colors">
              <FacebookIcon size={24} />
            </Link>
            <Link href="#" className="text-[#92adc9] hover:text-white transition-colors">
              <InstagramIcon size={24} />
            </Link>
          </div>

          <p className="text-[#92adc9] text-base font-normal leading-normal">
            © 2023 uTulz. Todos os direitos reservados.
          </p>
        </footer>
      </div>
    </footer>
  );
};

export default Footer;
