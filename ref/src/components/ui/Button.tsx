import { cn } from '@/utils/cn';
import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  asChild?: boolean;
  children: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(({
  variant = 'primary',
  size = 'md',
  className,
  children,
  asChild = false,
  ...props
}, ref) => {
  const baseClasses = 'flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg font-bold leading-normal tracking-[0.015em] transition-colors';

  const variantClasses = {
    primary: 'bg-[#1172d4] text-white hover:bg-[#0f5fb3]',
    secondary: 'bg-[#233648] text-white hover:bg-[#2a4155]',
    outline: 'border border-[#324d67] bg-transparent text-white hover:bg-[#192633]',
  };

  const sizeClasses = {
    sm: 'h-8 px-3 text-xs',
    md: 'h-10 px-4 text-sm',
    lg: 'h-12 px-5 text-base',
  };

  const classes = cn(
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    className
  );

  if (asChild) {
    const child = children as React.ReactElement<any>;
    return React.cloneElement(child, {
      className: cn(classes, child.props?.className),
      ref,
    });
  }

  return (
    <button
      ref={ref}
      className={classes}
      {...props}
    >
      <span className="truncate">{children}</span>
    </button>
  );
});

Button.displayName = 'Button';

export default Button;
