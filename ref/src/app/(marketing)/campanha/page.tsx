import CampaignHero from '@/components/CampaignHero';
import { TextHThreeIcon } from '@/components/Icons';
import TestimonialCard from '@/components/TestimonialCard';
import Button from '@/components/ui/Button';

export default function CampaignScreen() {
  const testimonials = [
    {
      quote: "uTulz revolucionou a forma como criamos campanhas. A plataforma é fácil de usar e os resultados são incríveis.",
      author: "Sofia Almeida",
      company: "Agência Digital"
    },
    {
      quote: "A capacidade de monitorar o desempenho em tempo real nos permite otimizar nossas campanhas e maximizar o ROI.",
      author: "<PERSON> Men<PERSON>",
      company: "Agência de Marketing"
    },
    {
      quote: "O suporte da equipe uTulz é excepcional. Eles estão sempre prontos para ajudar e garantir o sucesso de nossas campanhas.",
      author: "Isabela Costa",
      company: "Agência de Publicidade"
    }
  ];

  return (
    <div className="layout-content-container flex flex-col max-w-[960px] @[1440px]:max-w-[1200px] flex-1">
      {/* Hero Section */}
      <CampaignHero />

      {/* How It Works Section */}
      <h2 className="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
        Como Funciona
      </h2>
      <div className="grid grid-cols-[40px_1fr] gap-x-2 px-4">
        <div className="flex flex-col items-center gap-1 pt-3">
          <div className="text-white">
            <TextHThreeIcon size={24} />
          </div>
          <div className="w-[1.5px] bg-[#324d67] h-2 grow"></div>
        </div>
        <div className="flex flex-1 flex-col py-3">
          <p className="text-white text-base font-medium leading-normal">Selecione seus Agentes</p>
          <p className="text-[#92adc9] text-base font-normal leading-normal">
            Escolha os agentes ideais para sua campanha com base em suas habilidades e experiência.
          </p>
        </div>
        <div className="flex flex-col items-center gap-1">
          <div className="w-[1.5px] bg-[#324d67] h-2"></div>
          <div className="text-white">
            <TextHThreeIcon size={24} />
          </div>
          <div className="w-[1.5px] bg-[#324d67] h-2 grow"></div>
        </div>
        <div className="flex flex-1 flex-col py-3">
          <p className="text-white text-base font-medium leading-normal">Configure sua Campanha</p>
          <p className="text-[#92adc9] text-base font-normal leading-normal">
            Defina os parâmetros da sua campanha, incluindo orçamento, cronograma e metas.
          </p>
        </div>
        <div className="flex flex-col items-center gap-1 pb-3">
          <div className="w-[1.5px] bg-[#324d67] h-2"></div>
          <div className="text-white">
            <TextHThreeIcon size={24} />
          </div>
        </div>
        <div className="flex flex-1 flex-col py-3">
          <p className="text-white text-base font-medium leading-normal">Execute e Monitore</p>
          <p className="text-[#92adc9] text-base font-normal leading-normal">
            Acompanhe o desempenho da sua campanha em tempo real e faça ajustes conforme necessário.
          </p>
        </div>
      </div>

      {/* Testimonials Section */}
      <h2 className="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
        Depoimentos
      </h2>
      <div className="flex overflow-y-auto [-ms-scrollbar-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
        <div className="flex items-stretch p-4 gap-3">
          {testimonials.map((testimonial, index) => (
            <TestimonialCard
              key={index}
              quote={testimonial.quote}
              author={testimonial.author}
              company={testimonial.company}
            />
          ))}
        </div>
      </div>

      {/* CTA Section */}
      <div className="@container">
        <div className="flex flex-col justify-end gap-6 px-4 py-10 @[480px]:gap-8 @[480px]:px-10 @[480px]:py-20">
          <div className="flex flex-col gap-2 text-center">
            <h1 className="text-white tracking-light text-[32px] font-bold leading-tight @[480px]:text-4xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em] max-w-[720px]">
              Comece a Criar Campanhas Poderosas Hoje Mesmo
            </h1>
          </div>
          <div className="flex flex-1 justify-center">
            <div className="flex justify-center">
              <Button
                size="lg"
                className="@[480px]:h-12 @[480px]:px-5 @[480px]:text-base grow"
              >
                Ver Planos e Preços
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
