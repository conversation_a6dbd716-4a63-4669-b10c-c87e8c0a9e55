import FeatureCard from '@/components/FeatureCard';
import Hero from '@/components/Hero';
import { ChartLineIcon, MagicWandIcon, RobotIcon } from '@/components/Icons';
import PricingCard from '@/components/PricingCard';

export default function HomeScreen() {
    const features = [
        {
            icon: <MagicWandIcon size={24} />,
            title: "Automação de Tarefas",
            description: "Automatize tarefas repetitivas e libere sua equipe para focar em estratégias criativas e de alto impacto."
        },
        {
            icon: <RobotIcon size={24} />,
            title: "Geração de Conteúdo Inteligente",
            description: "Crie conteúdo de alta qualidade de forma rápida e eficiente, utilizando algoritmos inteligentes e personalizáveis."
        },
        {
            icon: <ChartLineIcon size={24} />,
            title: "Otimização de Campanhas",
            description: "Maximize o desempenho de suas campanhas com análises detalhadas e ajustes automáticos para atingir seus objetivos."
        }
    ];

    const pricingPlans = [
        {
            title: "Básico",
            price: "R$ 99",
            period: "/mês",
            features: [
                { text: "Acesso a agentes básicos", included: true },
                { text: "Geração de até 100 documentos/mês", included: true },
                { text: "Suporte por e-mail", included: true }
            ]
        },
        {
            title: "Profissional",
            price: "R$ 199",
            period: "/mês",
            features: [
                { text: "Acesso a todos os agentes", included: true },
                { text: "Geração ilimitada de documentos", included: true },
                { text: "Suporte prioritário", included: true },
                { text: "Integrações com outras ferramentas", included: true }
            ],
            highlighted: true
        },
        {
            title: "Empresarial",
            price: "R$ 399",
            period: "/mês",
            features: [
                { text: "Acesso a todos os agentes", included: true },
                { text: "Geração ilimitada de documentos", included: true },
                { text: "Suporte VIP", included: true },
                { text: "Integrações avançadas", included: true },
                { text: "Gerente de contas dedicado", included: true }
            ]
        }
    ];



    const ListBulletsIcon = () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
            <path d="M80,64a8,8,0,0,1,8-8H216a8,8,0,0,1,0,16H88A8,8,0,0,1,80,64Zm136,56H88a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16Zm0,64H88a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16ZM44,52A12,12,0,1,0,56,64,12,12,0,0,0,44,52Zm0,64a12,12,0,1,0,12,12A12,12,0,0,0,44,116Zm0,64a12,12,0,1,0,12,12A12,12,0,0,0,44,180Z"></path>
        </svg>
    );

    const CalendarIcon = () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
            <path d="M208,32H184V24a8,8,0,0,0-16,0v8H88V24a8,8,0,0,0-16,0v8H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32ZM72,48v8a8,8,0,0,0,16,0V48h80v8a8,8,0,0,0,16,0V48h24V80H48V48ZM208,208H48V96H208V208Zm-96-88v64a8,8,0,0,1-16,0V132.94l-4.42,2.22a8,8,0,0,1-7.16-14.32l16-8A8,8,0,0,1,112,120Zm59.16,30.45L152,176h16a8,8,0,0,1,0,16H136a8,8,0,0,1-6.4-12.8l28.78-38.37A8,8,0,1,0,145.07,132a8,8,0,1,1-13.85-8A24,24,0,0,1,176,136,23.76,23.76,0,0,1,171.16,150.45Z"></path>
        </svg>
    );

    const PresentationChartIcon = () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
            <path d="M216,40H136V24a8,8,0,0,0-16,0V40H40A16,16,0,0,0,24,56V176a16,16,0,0,0,16,16H79.36L57.75,219a8,8,0,0,0,12.5,10l29.59-37h56.32l29.59,37a8,8,0,1,0,12.5-10l-21.61-27H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40Zm0,136H40V56H216V176ZM104,120v24a8,8,0,0,1-16,0V120a8,8,0,0,1,16,0Zm32-16v40a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Zm32-16v56a8,8,0,0,1-16,0V88a8,8,0,0,1,16,0Z"></path>
        </svg>
    );



    return (
        <div className="layout-content-container flex flex-col max-w-[960px] @[1440px]:max-w-[1200px] flex-1">
            {/* Hero Section */}
            <Hero />

            {/* Benefits Section */}
            <h2 className="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
                Benefícios da uTulz
            </h2>
            <div className="flex flex-col gap-10 px-4 py-10 @container">
                <div className="flex flex-col gap-4">
                    <h1 className="text-white tracking-light text-[32px] font-bold leading-tight @[480px]:text-4xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em] max-w-[720px]">
                        Transforme sua Agência com uTulz
                    </h1>
                    <p className="text-white text-base font-normal leading-normal max-w-[720px]">
                        Descubra como a uTulz pode revolucionar a forma como sua agência opera, otimizando processos e impulsionando resultados.
                    </p>
                </div>
                <div className="grid grid-cols-1 @[480px]:grid-cols-2 @[768px]:grid-cols-3 gap-3 p-0">
                    {features.map((feature, index) => (
                        <FeatureCard
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            description={feature.description}
                        />
                    ))}
                </div>
            </div>

            {/* How It Works Section */}
            <h2 className="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
                Como Funciona
            </h2>
            <div className="grid grid-cols-[40px_1fr] gap-x-2 px-4">
                <div className="flex flex-col items-center gap-1 pt-5">
                    <div className="size-2 rounded-full bg-white"></div>
                    <div className="w-[1.5px] bg-[#324d67] h-4 grow"></div>
                </div>
                <div className="flex flex-1 flex-col py-3">
                    <p className="text-white text-base font-medium leading-normal">Escolha seu Agente</p>
                    <p className="text-[#92adc9] text-base font-normal leading-normal">
                        Selecione o agente virtual mais adequado para suas necessidades específicas.
                    </p>
                </div>
                <div className="flex flex-col items-center gap-1">
                    <div className="w-[1.5px] bg-[#324d67] h-4"></div>
                    <div className="size-2 rounded-full bg-white"></div>
                    <div className="w-[1.5px] bg-[#324d67] h-4 grow"></div>
                </div>
                <div className="flex flex-1 flex-col py-3">
                    <p className="text-white text-base font-medium leading-normal">Personalize</p>
                    <p className="text-[#92adc9] text-base font-normal leading-normal">
                        Ajuste as configurações e parâmetros para garantir resultados alinhados com seus objetivos.
                    </p>
                </div>
                <div className="flex flex-col items-center gap-1 pb-3">
                    <div className="w-[1.5px] bg-[#324d67] h-4"></div>
                    <div className="size-2 rounded-full bg-white"></div>
                </div>
                <div className="flex flex-1 flex-col py-3">
                    <p className="text-white text-base font-medium leading-normal">Gere</p>
                    <p className="text-[#92adc9] text-base font-normal leading-normal">
                        Gere documentos e arquivos de alta qualidade com apenas alguns cliques.
                    </p>
                </div>
            </div>

            {/* Pricing Section */}
            <h2 className="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
                Planos e Preços
            </h2>
            <div className="grid grid-cols-1 @[480px]:grid-cols-2 @[768px]:grid-cols-3 @[1440px]:grid-cols-4 gap-2.5 px-4 py-3">
                {pricingPlans.map((plan, index) => (
                    <PricingCard
                        key={index}
                        title={plan.title}
                        price={plan.price}
                        period={plan.period}
                        features={plan.features}
                        highlighted={plan.highlighted}
                    />
                ))}
            </div>
        </div>
    );
}