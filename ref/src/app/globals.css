/* Import Google Fonts - must be first */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700;900&family=Noto+Sans:wght@400;500;700;900&display=swap');

@import "tailwindcss";


@layer base {
  :root {
    --background: 200 20% 98%;
    --foreground: 200 10% 20%;
    --card: 0 0% 100%;
    --card-foreground: 200 10% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 200 10% 20%;
    --primary: 195 86% 40%;
    --primary-foreground: 0 0% 100%;
    --secondary: 200 15% 90%;
    --secondary-foreground: 195 86% 30%;
    --muted: 200 15% 94%;
    --muted-foreground: 200 10% 45%;
    --accent: 195 86% 45%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 200 10% 88%;
    --input: 0 0% 100%;
    --ring: 195 86% 45%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: 195 86% 40%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 0 0% 100%;
    --sidebar-primary-foreground: 195 86% 30%;
    --sidebar-accent: 195 86% 35%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 195 86% 35%;
    --sidebar-ring: 195 86% 45%;
  }

  .dark {
    --background: 204 10% 7.8%;
    --foreground: 200 15% 95%;
    --card: 200 10% 12%;
    --card-foreground: 200 15% 95%;
    --popover: 204 10% 7.8%;
    --popover-foreground: 200 15% 95%;
    --primary: 195 86% 50%;
    --primary-foreground: 0 0% 100%;
    --secondary: 204 14% 14%;
    --secondary-foreground: 200 15% 95%;
    --muted: 200 10% 15%;
    --muted-foreground: 200 15% 60%;
    --accent: 195 86% 55%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 206 11% 29.6%;
    --input: 204 14% 14%;
    --ring: 195 86% 55%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 204 10% 7.8%;
    --sidebar-foreground: 200 15% 95%;
    --sidebar-primary: 195 86% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 204 14% 14%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 206 11% 29.6%;
    --sidebar-ring: 195 86% 55%;
  }
}

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-white text-gray-900;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}