// Common types for the uTulz platform

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user' | 'agency';
  createdAt: Date;
  updatedAt: Date;
}

export interface Agency {
  id: string;
  name: string;
  description?: string;
  website?: string;
  users: User[];
  subscription: Subscription;
  createdAt: Date;
  updatedAt: Date;
}

export interface Subscription {
  id: string;
  plan: 'basic' | 'professional' | 'enterprise';
  status: 'active' | 'inactive' | 'cancelled' | 'past_due';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
}

export interface AIAgent {
  id: string;
  name: string;
  description: string;
  category: AgentCategory;
  capabilities: string[];
  inputTypes: string[];
  outputTypes: string[];
  isActive: boolean;
  version: string;
}

export type AgentCategory = 
  | 'content-generation'
  | 'campaign-optimization'
  | 'data-analysis'
  | 'creative-design'
  | 'social-media'
  | 'email-marketing'
  | 'seo-optimization';

export interface Campaign {
  id: string;
  name: string;
  description?: string;
  agencyId: string;
  agents: AIAgent[];
  status: 'draft' | 'active' | 'paused' | 'completed';
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  settings: CampaignSettings;
}

export interface CampaignSettings {
  autoOptimization: boolean;
  targetAudience?: string;
  budget?: number;
  duration?: {
    start: Date;
    end: Date;
  };
  goals: string[];
}

export interface Document {
  id: string;
  name: string;
  type: DocumentType;
  content: string;
  metadata: DocumentMetadata;
  agentId: string;
  campaignId?: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export type DocumentType = 
  | 'text'
  | 'image'
  | 'video'
  | 'audio'
  | 'pdf'
  | 'presentation'
  | 'spreadsheet';

export interface DocumentMetadata {
  size: number;
  format: string;
  tags: string[];
  version: number;
  isPublic: boolean;
}

export interface PricingPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: PricingFeature[];
  limits: PlanLimits;
  isPopular?: boolean;
}

export interface PricingFeature {
  text: string;
  included: boolean;
  description?: string;
}

export interface PlanLimits {
  documentsPerMonth: number | 'unlimited';
  agentsAccess: 'basic' | 'all';
  supportLevel: 'email' | 'priority' | 'vip';
  integrations: boolean;
  advancedFeatures: boolean;
  dedicatedManager: boolean;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
  timestamp: Date;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// Form types
export interface ContactForm {
  name: string;
  email: string;
  company?: string;
  message: string;
  subject: string;
}

export interface SignupForm {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  agencyName?: string;
  acceptTerms: boolean;
}

export interface LoginForm {
  email: string;
  password: string;
  rememberMe: boolean;
}
