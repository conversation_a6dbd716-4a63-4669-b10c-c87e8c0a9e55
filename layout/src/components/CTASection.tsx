import { <PERSON>R<PERSON>, <PERSON>, Gift, Sparkles } from "lucide-react";
import { Badge } from "./ui/badge";
import { <PERSON><PERSON> } from "./ui/button";
import { Card } from "./ui/card";

interface CTASectionProps {
  onNavigateToPricing?: () => void;
}

export function CTASection({ onNavigateToPricing }: CTASectionProps) {
  return (
    <section className="py-20 md:py-32">
      <div className="container px-4 md:px-8">
        <Card className="relative overflow-hidden bg-gradient-to-br from-primary to-primary/80 text-white p-8 md:p-12">
          {/* Background pattern */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
              backgroundSize: '60px 60px'
            }} />
          </div>

          <div className="relative text-center space-y-8 max-w-4xl mx-auto">
            {/* Header */}
            <div className="space-y-4">
              <Badge variant="secondary" className="w-fit mx-auto bg-white/20 text-white border-white/30">
                <Gift className="w-3 h-3 mr-2" />
                Oferta Limitada
              </Badge>

              <h2 className="text-3xl md:text-4xl lg:text-5xl tracking-tight">
                Pronto para acelerar suas{" "}
                <span className="text-yellow-300">entregas criativas</span>?
              </h2>

              <p className="text-xl text-white/90 max-w-2xl mx-auto">
                Junte-se a centenas de agências e profissionais que já transformaram
                sua produtividade com a uTulz. Comece hoje mesmo!
              </p>
            </div>

            {/* Features list */}
            <div className="grid md:grid-cols-3 gap-6 py-8">
              <div className="flex items-center justify-center space-x-3">
                <Sparkles className="w-5 h-5 text-yellow-300" />
                <span>Acesso a todas as ferramentas</span>
              </div>
              <div className="flex items-center justify-center space-x-3">
                <Clock className="w-5 h-5 text-yellow-300" />
                <span>14 dias grátis</span>
              </div>
              <div className="flex items-center justify-center space-x-3">
                <Gift className="w-5 h-5 text-yellow-300" />
                <span>Sem compromisso</span>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button size="lg" variant="secondary" className="group" onClick={onNavigateToPricing}>
                Começar Teste Grátis
                <ArrowRight className="w-4 h-4 ml-2 transition-transform group-hover:translate-x-1" />
              </Button>
              <Button size="lg" variant="outline" className="bg-transparent border-white text-white hover:bg-white hover:text-primary">
                Agendar Demo
              </Button>
            </div>

            {/* Trust indicators */}
            <div className="pt-8 border-t border-white/20">
              <p className="text-white/70 text-sm">
                ✓ Sem cartão de crédito  •  ✓ Configuração em 2 minutos  •  ✓ Suporte em português
              </p>
            </div>
          </div>
        </Card>
      </div>
    </section>
  );
}