import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CampaignStep } from "@/entities/CampaignStep";
import { StepHistory } from "@/entities/StepHistory";
import {
    AlertTriangle,
    Download,
    Edit3,
    FileText,
    RefreshCw,
    Save
} from "lucide-react";
import React, { useEffect, useState } from "react";

export default function BriefingEditor({ briefingContent, campaignStep, onContentUpdate }) {
    const [content, setContent] = useState(briefingContent || "");
    const [isEditing, setIsEditing] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [hasChanges, setHasChanges] = useState(false);

    useEffect(() => {
        setContent(briefingContent || "");
    }, [briefingContent]);

    const handleContentChange = (e) => {
        setContent(e.target.value);
        setHasChanges(e.target.value !== briefingContent);
    };

    const saveChanges = async () => {
        setIsSaving(true);

        try {
            const newVersion = (campaignStep.version || 1) + 1;

            // Atualizar etapa
            await CampaignStep.update(campaignStep.id, {
                briefing_content: content,
                version: newVersion,
                is_edited: true
            });

            // Salvar histórico
            await StepHistory.create({
                campaign_step_id: campaignStep.id,
                version_number: newVersion,
                content: content,
                change_type: "manual_edit",
                change_description: "Briefing editado manualmente"
            });

            setHasChanges(false);
            setIsEditing(false);
            onContentUpdate();

        } catch (error) {
            console.error("Erro ao salvar alterações:", error);
        }

        setIsSaving(false);
    };

    const exportToPDF = async () => {
        // Implementar exportação para PDF
        console.log("Exportar para PDF:", content);
    };

    const exportToPPT = async () => {
        // Implementar exportação para PPT
        console.log("Exportar para PPT:", content);
    };

    return (
        <Card className="h-fit">
            <CardHeader>
                <div className="flex justify-between items-start">
                    <CardTitle className="flex items-center gap-2">
                        <FileText className="w-5 h-5 text-navy-600 dark:text-teal-400" />
                        Briefing da Campanha
                        {campaignStep?.is_edited && (
                            <span className="text-xs bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 px-2 py-1 rounded">
                                Editado
                            </span>
                        )}
                    </CardTitle>
                    <div className="flex gap-2">
                        {!isEditing ? (
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setIsEditing(true)}
                            >
                                <Edit3 className="w-4 h-4 mr-2" />
                                Editar
                            </Button>
                        ) : (
                            <>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                        setContent(briefingContent);
                                        setIsEditing(false);
                                        setHasChanges(false);
                                    }}
                                >
                                    Cancelar
                                </Button>
                                <Button
                                    size="sm"
                                    onClick={saveChanges}
                                    disabled={isSaving || !hasChanges}
                                    className="bg-navy-600 hover:bg-navy-700 dark:bg-teal-500 dark:hover:bg-teal-600 dark:text-slate-900"
                                >
                                    {isSaving ? (
                                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                    ) : (
                                        <Save className="w-4 h-4 mr-2" />
                                    )}
                                    Salvar
                                </Button>
                            </>
                        )}
                    </div>
                </div>
            </CardHeader>
            <CardContent className="space-y-4">
                {campaignStep?.is_edited && (
                    <Alert className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
                        <AlertTriangle className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                        <AlertDescription className="text-yellow-700 dark:text-yellow-300">
                            Este briefing foi editado manualmente. As próximas etapas podem não refletir essas mudanças automaticamente.
                        </AlertDescription>
                    </Alert>
                )}

                {isEditing ? (
                    <textarea
                        value={content}
                        onChange={handleContentChange}
                        className="w-full h-96 p-4 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100 font-mono text-sm resize-none"
                        placeholder="Conteúdo do briefing em HTML..."
                    />
                ) : (
                    <div
                        className="prose prose-slate dark:prose-invert max-w-none bg-white dark:bg-slate-800 p-6 rounded-lg border border-slate-200 dark:border-slate-700 min-h-96"
                        dangerouslySetInnerHTML={{ __html: content }}
                    />
                )}

                <div className="flex justify-between items-center pt-4 border-t border-slate-200 dark:border-slate-700">
                    <div className="flex gap-2">
                        <Button variant="outline" size="sm" onClick={exportToPDF}>
                            <Download className="w-4 h-4 mr-2" />
                            PDF
                        </Button>
                        <Button variant="outline" size="sm" onClick={exportToPPT}>
                            <Download className="w-4 h-4 mr-2" />
                            PPT
                        </Button>
                    </div>
                    <div className="text-xs text-slate-500 dark:text-slate-400">
                        Versão {campaignStep?.version || 1}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}