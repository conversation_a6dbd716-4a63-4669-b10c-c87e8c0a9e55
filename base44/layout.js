
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarProvider,
    SidebarTrigger,
} from "@/components/ui/sidebar";
import { User } from "@/entities/User";
import { createPageUrl } from "@/utils";
import {
    Bell,
    FileText,
    LayoutDashboard,
    LogOut, // Added Sun icon
    Moon // Added Moon icon
    ,
    Search,
    Settings,
    Sun,
    User as UserIcon, Wrench,
    Zap
} from "lucide-react";
import React, { useEffect, useState } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import ThemeSwitcher from "../components/ThemeSwitcher"; // Added ThemeSwitcher import

const publicPages = ["Home", "Tools", "Pricing"];

const navigationItems = [
    {
        title: "Dashboard",
        url: createPageUrl("Dashboard"),
        icon: LayoutDashboard,
    },
    {
        title: "Campanhas",
        url: createPageUrl("Campaigns"),
        icon: Zap,
    },
    {
        title: "Agentes Rápidos",
        url: createPageUrl("QuickAgents"),
        icon: Wrench,
    },
    {
        title: "Templates",
        url: createPageUrl("Templates"),
        icon: FileText,
    },
    {
        title: "Integrações",
        url: createPageUrl("Integrations"),
        icon: Settings,
    },
];

export default function Layout({ children, currentPageName }) {
    const location = useLocation();
    const navigate = useNavigate();
    const [user, setUser] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const isPublicPage = publicPages.includes(currentPageName);

    useEffect(() => {
        loadUser();
    }, []);

    const loadUser = async () => {
        try {
            const userData = await User.me();
            setUser(userData);
        } catch (error) {
            setUser(null);
        }
        setIsLoading(false);
    };

    const handleLogout = async () => {
        await User.logout();
        setUser(null);
        navigate(createPageUrl("Home"));
    };

    // Public layout for landing pages
    if (isPublicPage) {
        return (
            <div className="min-h-screen bg-slate-50 dark:bg-slate-950">
                <header className="bg-white/80 dark:bg-slate-950/80 backdrop-blur-sm border-b border-slate-200 dark:border-slate-800 sticky top-0 z-50">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="flex justify-between items-center h-16">
                            <Link to={createPageUrl("Home")} className="flex items-center gap-2">
                                <div className="w-8 h-8 bg-gradient-to-r from-navy-600 to-teal-500 rounded-lg flex items-center justify-center">
                                    <Zap className="w-5 h-5 text-white" />
                                </div>
                                <span className="text-xl font-bold text-slate-900 dark:text-slate-50">uTulz</span>
                            </Link>

                            <nav className="hidden md:flex items-center gap-8">
                                <Link
                                    to={createPageUrl("Home")}
                                    className={`text-sm font-medium transition-colors ${currentPageName === "Home" ? "text-navy-600 dark:text-teal-400" : "text-slate-600 dark:text-slate-400 hover:text-navy-600 dark:hover:text-teal-400"
                                        }`}
                                >
                                    Início
                                </Link>
                                <Link
                                    to={createPageUrl("Tools")}
                                    className={`text-sm font-medium transition-colors ${currentPageName === "Tools" ? "text-navy-600 dark:text-teal-400" : "text-slate-600 dark:text-slate-400 hover:text-navy-600 dark:hover:text-teal-400"
                                        }`}
                                >
                                    Ferramentas
                                </Link>
                                <Link
                                    to={createPageUrl("Pricing")}
                                    className={`text-sm font-medium transition-colors ${currentPageName === "Pricing" ? "text-navy-600 dark:text-teal-400" : "text-slate-600 dark:text-slate-400 hover:text-navy-600 dark:hover:text-teal-400"
                                        }`}
                                >
                                    Preços
                                </Link>
                            </nav>

                            <div className="flex items-center gap-2">
                                <ThemeSwitcher />
                                {user ? (
                                    <Link to={createPageUrl("Dashboard")}>
                                        <Button className="bg-navy-600 hover:bg-navy-700 dark:bg-teal-500 dark:hover:bg-teal-600 dark:text-slate-900">
                                            Dashboard
                                        </Button>
                                    </Link>
                                ) : (
                                    <>
                                        <Button variant="ghost" className="text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800" onClick={() => User.login()}>
                                            Entrar
                                        </Button>
                                        <Button
                                            className="bg-navy-600 hover:bg-navy-700 dark:bg-teal-500 dark:hover:bg-teal-600 dark:text-slate-900"
                                            onClick={() => User.login()}
                                        >
                                            Criar conta
                                        </Button>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </header>

                <main className="flex-1">
                    {children}
                </main>

                <footer className="bg-white dark:bg-slate-950 border-t border-slate-200 dark:border-slate-800 py-8">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                                <div className="w-6 h-6 bg-gradient-to-r from-navy-600 to-teal-500 rounded-md flex items-center justify-center">
                                    <Zap className="w-4 h-4 text-white" />
                                </div>
                                <span className="font-semibold text-slate-700 dark:text-slate-300">uTulz</span>
                            </div>
                            <div className="flex gap-6 text-sm text-slate-600 dark:text-slate-400">
                                <Link to="/terms" className="hover:text-navy-600 dark:hover:text-teal-400 transition-colors">
                                    Termos de Uso
                                </Link>
                                <Link to="/privacy" className="hover:text-navy-600 dark:hover:text-teal-400 transition-colors">
                                    Privacidade
                                </Link>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        );
    }

    // Authenticated layout with sidebar
    if (!user && !isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
                <div className="text-center">
                    <h2 className="text-2xl font-bold text-slate-900 dark:text-slate-50 mb-4">Acesso necessário</h2>
                    <p className="text-slate-600 dark:text-slate-400 mb-6">Faça login para acessar sua conta uTulz</p>
                    <Button onClick={() => User.login()} className="bg-navy-600 hover:bg-navy-700 dark:bg-teal-500 dark:hover:bg-teal-600 dark:text-slate-900">
                        Fazer Login
                    </Button>
                </div>
            </div>
        );
    }

    return (
        <SidebarProvider>
            <div className="min-h-screen flex w-full bg-slate-50 dark:bg-slate-950 text-slate-900 dark:text-slate-50">
                <Sidebar className="border-r border-slate-200 dark:border-slate-800 bg-white dark:bg-slate-900">
                    <SidebarHeader className="border-b border-slate-200 dark:border-slate-800 p-4">
                        <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-gradient-to-r from-navy-600 to-teal-500 rounded-xl flex items-center justify-center">
                                <Zap className="w-6 h-6 text-white" />
                            </div>
                            <div>
                                <h2 className="font-bold text-slate-900 dark:text-slate-50">uTulz</h2>
                                <p className="text-xs text-slate-500 dark:text-slate-400">Suas ferramentas para campanhas</p>
                            </div>
                        </div>
                    </SidebarHeader>

                    <SidebarContent className="p-3">
                        <SidebarGroup>
                            <SidebarGroupContent>
                                <SidebarMenu>
                                    {navigationItems.map((item) => (
                                        <SidebarMenuItem key={item.title}>
                                            <SidebarMenuButton
                                                asChild
                                                className={`hover:bg-navy-50 hover:text-navy-700 dark:hover:bg-slate-800 dark:hover:text-slate-200 transition-all duration-200 rounded-lg mb-1 ${location.pathname === item.url ? 'bg-navy-50 text-navy-700 border border-navy-200 dark:bg-slate-800 dark:text-teal-400 dark:border-slate-700' : 'dark:text-slate-300'
                                                    }`}
                                            >
                                                <Link to={item.url} className="flex items-center gap-3 px-3 py-3">
                                                    <item.icon className="w-5 h-5" />
                                                    <span className="font-medium">{item.title}</span>
                                                </Link>
                                            </SidebarMenuButton>
                                        </SidebarMenuItem>
                                    ))}
                                </SidebarMenu>
                            </SidebarGroupContent>
                        </SidebarGroup>
                    </SidebarContent>

                    <SidebarFooter className="border-t border-slate-200 dark:border-slate-800 p-4">
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="w-full justify-start gap-3 px-3 py-3 dark:hover:bg-slate-800">
                                    <div className="w-8 h-8 bg-slate-200 dark:bg-slate-700 rounded-full flex items-center justify-center">
                                        <UserIcon className="w-4 h-4 text-slate-600 dark:text-slate-300" />
                                    </div>
                                    <div className="flex-1 min-w-0 text-left">
                                        <p className="font-medium text-slate-900 dark:text-slate-50 text-sm truncate">
                                            {user?.full_name || "Usuário"}
                                        </p>
                                        <p className="text-xs text-slate-500 dark:text-slate-400 truncate">{user?.email}</p>
                                    </div>
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-56">
                                <DropdownMenuItem onClick={() => {
                                    const currentTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light';
                                    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                                    if (newTheme === 'dark') {
                                        document.documentElement.classList.add('dark');
                                    } else {
                                        document.documentElement.classList.remove('dark');
                                    }
                                    localStorage.setItem('theme', newTheme);
                                }}>
                                    <Sun className="h-4 w-4 mr-2 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
                                    <Moon className="absolute h-4 w-4 mr-2 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
                                    <span>Alterar Tema</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={handleLogout}>
                                    <LogOut className="w-4 h-4 mr-2" />
                                    Sair
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </SidebarFooter>
                </Sidebar>

                <main className="flex-1 flex flex-col">
                    <header className="bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-800 px-6 py-4 lg:hidden">
                        <div className="flex items-center justify-between">
                            <SidebarTrigger className="hover:bg-slate-100 dark:hover:bg-slate-800 p-2 rounded-lg transition-colors duration-200" />
                            <div className="flex items-center gap-2">
                                <Search className="w-5 h-5 text-slate-400" />
                                <Input placeholder="Buscar..." className="w-40 bg-transparent" />
                            </div>
                            <div className="flex items-center gap-2">
                                <ThemeSwitcher />
                                <Bell className="w-5 h-5 text-slate-400" />
                            </div>
                        </div>
                    </header>

                    <div className="flex-1 overflow-auto">
                        {children}
                    </div>
                </main>
            </div>

            <style>{`
        :root {
          --background: 210 40% 98%;
          --foreground: 222.2 84% 4.9%;
          --card: 210 40% 98%;
          --card-foreground: 222.2 84% 4.9%;
          --popover: 210 40% 98%;
          --popover-foreground: 222.2 84% 4.9%;
          --primary: 222.2 47.4% 11.2%;
          --primary-foreground: 210 40% 98%;
          --secondary: 210 40% 96.1%;
          --secondary-foreground: 222.2 47.4% 11.2%;
          --muted: 210 40% 96.1%;
          --muted-foreground: 215.4 16.3% 46.9%;
          --accent: 210 40% 96.1%;
          --accent-foreground: 222.2 47.4% 11.2%;
          --destructive: 0 84.2% 60.2%;
          --destructive-foreground: 210 40% 98%;
          --border: 214.3 31.8% 91.4%;
          --input: 214.3 31.8% 91.4%;
          --ring: 222.2 84% 4.9%;
          --radius: 0.5rem;
          --navy-50: #f8fafc;
          --navy-100: #f1f5f9;
          --navy-200: #e2e8f0;
          --navy-600: #1e293b;
          --navy-700: #0f172a;
          --teal-500: #14b8a6;
        }

        .dark {
          --background: 222.2 84% 4.9%;
          --foreground: 210 40% 98%;
          --card: 222.2 84% 4.9%;
          --card-foreground: 210 40% 98%;
          --popover: 222.2 84% 4.9%;
          --popover-foreground: 210 40% 98%;
          --primary: 210 40% 98%;
          --primary-foreground: 222.2 47.4% 11.2%;
          --secondary: 217.2 32.6% 17.5%;
          --secondary-foreground: 210 40% 98%;
          --muted: 217.2 32.6% 17.5%;
          --muted-foreground: 215 20.2% 65.1%;
          --accent: 217.2 32.6% 17.5%;
          --accent-foreground: 210 40% 98%;
          --destructive: 0 62.8% 30.6%;
          --destructive-foreground: 210 40% 98%;
          --border: 217.2 32.6% 17.5%;
          --input: 217.2 32.6% 17.5%;
          --ring: 212.7 26.8% 83.9%;
          --navy-50: #1e293b;
          --navy-100: #0f172a;
          --navy-200: #334155;
          --navy-600: #f1f5f9;
          --navy-700: #f1f5f9;
          --teal-500: #14b8a6;
        }

        .bg-navy-50 { background-color: var(--navy-50); }
        .bg-navy-600 { background-color: var(--navy-600); }
        .bg-navy-700 { background-color: var(--navy-700); }
        .text-navy-600 { color: var(--navy-600); }
        .text-navy-700 { color: var(--navy-700); }
        .border-navy-200 { border-color: var(--navy-200); }
        .hover\\:bg-navy-50:hover { background-color: var(--navy-50); }
        .hover\\:bg-navy-700:hover { background-color: var(--navy-700); }
        .hover\\:text-navy-600:hover { color: var(--navy-600); }

        /* Specific dark mode overrides for existing Tailwind classes */
        .dark .bg-slate-50 { background-color: #020617; }
        .dark .bg-white { background-color: #0f172a; }
        .dark .text-slate-900 { color: #f8fafc; }
        .dark .text-slate-800 { color: #f1f5f9; }
        .dark .text-slate-700 { color: #e2e8f0; }
        .dark .text-slate-600 { color: #94a3b8; }
        .dark .border-slate-200 { border-color: #334155; }
        .dark .border-slate-300 { border-color: #475569; }
        .dark .hover\\:bg-slate-50:hover { background-color: #1e293b; }
        .dark .dark\\:bg-slate-950 { background-color: #020617; }
        .dark .dark\\:bg-slate-900 { background-color: #0f172a; }
        .dark .dark\\:bg-slate-800 { background-color: #1e293b; }
      `}</style>
        </SidebarProvider>
    );
}
