
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { AgentRun } from "@/entities/AgentRun";
import { InvokeLLM } from "@/integrations/Core";
import {
    AlertTriangle,
    Download,
    FileText,
    HelpCircle,
    Loader2,
    MessageCircle,
    Radar,
    Sparkles,
    Users
} from "lucide-react";
import React, { useState } from "react";

const agents = [
    {
        id: "intake_radar",
        name: "Intake Radar",
        description: "Transforme conversas soltas em briefings estruturados",
        icon: Radar,
        color: "from-blue-500 to-cyan-500",
        prompt: "Analise esta conversa com o cliente e organize as informações em um briefing estruturado. Identifique objetivos, restrições, riscos e próximos passos:",
        placeholder: "Cole aqui a conversa com o cliente (WhatsApp, email, call notes)..."
    },
    {
        id: "briefing_builder",
        name: "Briefing Builder",
        description: "Crie briefings completos com todas as seções necessárias",
        icon: FileText,
        color: "from-green-500 to-emerald-500",
        prompt: "Crie um briefing completo de campanha baseado nestas informações. Inclua todas as seções padrão e identifique gaps:",
        placeholder: "Descreva o contexto, objetivos, público e restrições da campanha..."
    },
    {
        id: "critical_questions",
        name: "Perguntas Críticas",
        description: "Identifique gaps no briefing antes de começar",
        icon: HelpCircle,
        color: "from-orange-500 to-red-500",
        prompt: "Analise este briefing e liste as perguntas críticas que precisam ser respondidas antes de prosseguir com a campanha:",
        placeholder: "Cole aqui o briefing para análise..."
    },
    {
        id: "personas",
        name: "Personas",
        description: "Desenvolva personas detalhadas baseadas no público",
        icon: Users,
        color: "from-purple-500 to-pink-500",
        prompt: "Crie 2-3 personas detalhadas baseadas neste briefing. Inclua nome, resumo, dores, ganhos, canais e mensagens:",
        placeholder: "Cole o briefing aprovado ou descreva o público-alvo..."
    }
];

export default function QuickAgents() {
    const [selectedAgent, setSelectedAgent] = useState(null);
    const [input, setInput] = useState("");
    const [output, setOutput] = useState("");
    const [isRunning, setIsRunning] = useState(false);
    const [assumptions, setAssumptions] = useState([]);
    const [questions, setQuestions] = useState([]);

    const runAgent = async () => {
        if (!selectedAgent || !input.trim()) return;

        setIsRunning(true);
        setOutput("");
        setAssumptions([]);
        setQuestions([]);

        try {
            // Create agent run record
            const agentRun = await AgentRun.create({
                agent_type: selectedAgent.id,
                input_data: { input: input.trim() },
                status: "running"
            });

            // Run the AI agent
            const result = await InvokeLLM({
                prompt: `${selectedAgent.prompt}\n\nContexto do cliente:\n${input.trim()}\n\nIMPORTANTE: Seja claro sobre suposições feitas e dúvidas que precisam ser esclarecidas. Use linguagem profissional mas acessível.`,
                response_json_schema: {
                    type: "object",
                    properties: {
                        content: { type: "string", description: "Conteúdo principal gerado" },
                        assumptions: {
                            type: "array",
                            items: { type: "string" },
                            description: "Suposições feitas durante a análise"
                        },
                        questions: {
                            type: "array",
                            items: { type: "string" },
                            description: "Dúvidas que precisam ser esclarecidas"
                        }
                    }
                }
            });

            setOutput(result.content || "Conteúdo gerado com sucesso");
            setAssumptions(result.assumptions || []);
            setQuestions(result.questions || []);

            // Update agent run with results
            await AgentRun.update(agentRun.id, {
                output_content: result.content,
                status: "completed",
                assumptions: result.assumptions,
                questions: result.questions
            });

        } catch (error) {
            console.error("Erro ao executar agente:", error);
            setOutput("Erro ao processar. Tente novamente.");
        }

        setIsRunning(false);
    };

    return (
        <div className="p-6 lg:p-8 bg-slate-50 min-h-screen">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-slate-900 mb-2">Agentes Rápidos</h1>
                    <p className="text-slate-600">
                        Execute agentes de IA para acelerar tarefas específicas
                    </p>
                </div>

                <div className="grid lg:grid-cols-3 gap-8">
                    {/* Agents List */}
                    <div className="lg:col-span-1">
                        <Card className="border border-slate-200">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Sparkles className="w-5 h-5 text-teal-500" />
                                    Escolha uma ferramenta
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="p-4">
                                <div className="space-y-3">
                                    {agents.map((agent) => (
                                        <div
                                            key={agent.id}
                                            onClick={() => setSelectedAgent(agent)}
                                            className={`p-4 rounded-lg cursor-pointer transition-all duration-200 ${selectedAgent?.id === agent.id
                                                    ? "bg-navy-50 border-2 border-navy-300"
                                                    : "border border-slate-200 hover:bg-slate-50"
                                                }`}
                                        >
                                            <div className="flex items-start gap-3">
                                                <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${agent.color} flex items-center justify-center flex-shrink-0`}>
                                                    <agent.icon className="w-5 h-5 text-white" />
                                                </div>
                                                <div className="flex-1 min-w-0">
                                                    <h4 className="font-semibold text-slate-900 mb-1">
                                                        {agent.name}
                                                    </h4>
                                                    <p className="text-sm text-slate-600 leading-relaxed">
                                                        {agent.description}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Agent Interface */}
                    <div className="lg:col-span-2">
                        {selectedAgent ? (
                            <div className="space-y-6">
                                {/* Input Area */}
                                <Card className="border border-slate-200">
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <selectedAgent.icon className="w-5 h-5 text-navy-600" />
                                            {selectedAgent.name}
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <Textarea
                                            value={input}
                                            onChange={(e) => setInput(e.target.value)}
                                            placeholder={selectedAgent.placeholder}
                                            className="h-32"
                                        />
                                        <Button
                                            onClick={runAgent}
                                            disabled={isRunning || !input.trim()}
                                            className="bg-navy-600 hover:bg-navy-700"
                                        >
                                            {isRunning ? (
                                                <>
                                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                                    Processando...
                                                </>
                                            ) : (
                                                <>
                                                    <Sparkles className="w-4 h-4 mr-2" />
                                                    Executar Agente
                                                </>
                                            )}
                                        </Button>
                                    </CardContent>
                                </Card>

                                {/* Output Area */}
                                {(output || assumptions.length > 0 || questions.length > 0) && (
                                    <div className="space-y-4">
                                        {/* Alerts for assumptions and questions */}
                                        {assumptions.length > 0 && (
                                            <Alert className="bg-yellow-50 border-yellow-200">
                                                <AlertTriangle className="w-4 h-4 text-yellow-600" />
                                                <AlertDescription>
                                                    <strong className="text-yellow-800">Suposições feitas:</strong>
                                                    <ul className="mt-2 text-sm text-yellow-700">
                                                        {assumptions.map((assumption, index) => (
                                                            <li key={index} className="ml-4 list-disc">{assumption}</li>
                                                        ))}
                                                    </ul>
                                                </AlertDescription>
                                            </Alert>
                                        )}

                                        {questions.length > 0 && (
                                            <Alert className="bg-blue-50 border-blue-200">
                                                <MessageCircle className="w-4 h-4 text-blue-600" />
                                                <AlertDescription>
                                                    <strong className="text-blue-800">Dúvidas para esclarecer:</strong>
                                                    <ul className="mt-2 text-sm text-blue-700">
                                                        {questions.map((question, index) => (
                                                            <li key={index} className="ml-4 list-disc">{question}</li>
                                                        ))}
                                                    </ul>
                                                </AlertDescription>
                                            </Alert>
                                        )}

                                        {/* Main Output */}
                                        <Card className="border border-slate-200">
                                            <CardHeader>
                                                <CardTitle className="flex items-center justify-between">
                                                    Resultado
                                                    <div className="flex gap-2">
                                                        <Button variant="outline" size="sm">
                                                            <Download className="w-4 h-4 mr-2" />
                                                            Exportar PDF
                                                        </Button>
                                                    </div>
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <div className="bg-white p-6 rounded-lg border border-slate-200 min-h-[300px]">
                                                    <pre className="whitespace-pre-wrap text-sm text-slate-700 leading-relaxed">
                                                        {output}
                                                    </pre>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <Card className="border border-slate-200 h-96 flex items-center justify-center">
                                <CardContent className="text-center">
                                    <Sparkles className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                                    <h3 className="text-lg font-semibold text-slate-900 mb-2">
                                        Selecione uma ferramenta
                                    </h3>
                                    <p className="text-slate-600">
                                        Escolha um agente na lista ao lado para começar
                                    </p>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
