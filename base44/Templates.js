
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Template } from "@/entities/Template";
import {
    Download,
    Eye,
    FileText,
    Plus,
    Search, Star
} from "lucide-react";
import React, { useCallback, useEffect, useState } from "react";

const templateTypes = [
    { value: "all", label: "Todos" },
    { value: "briefing", label: "Briefings" },
    { value: "strategy", label: "Estratégias" },
    { value: "media_plan", label: "Planos de Mídia" },
    { value: "report", label: "Relatórios" }
];

export default function Templates() {
    const [templates, setTemplates] = useState([]);
    const [filteredTemplates, setFilteredTemplates] = useState([]);
    const [activeTab, setActiveTab] = useState("all");
    const [searchTerm, setSearchTerm] = useState("");
    const [isLoading, setIsLoading] = useState(true);

    const loadTemplates = async () => {
        try {
            const data = await Template.list("-usage_count");
            setTemplates(data);
        } catch (error) {
            console.error("Erro ao carregar templates:", error);
        }
        setIsLoading(false);
    };

    const filterTemplates = useCallback(() => {
        let filtered = templates;

        if (activeTab !== "all") {
            filtered = filtered.filter(t => t.type === activeTab);
        }

        if (searchTerm) {
            filtered = filtered.filter(t =>
                t.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                t.description.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        setFilteredTemplates(filtered);
    }, [templates, activeTab, searchTerm]); // Dependencies for useCallback

    useEffect(() => {
        loadTemplates();
    }, []);

    useEffect(() => {
        filterTemplates();
    }, [filterTemplates]); // Dependency for useEffect is the memoized function

    return (
        <div className="p-6 lg:p-8 bg-slate-50 min-h-screen">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-slate-900">Templates</h1>
                        <p className="text-slate-600 mt-1">
                            Modelos prontos para acelerar seu trabalho
                        </p>
                    </div>
                    <Button className="bg-navy-600 hover:bg-navy-700">
                        <Plus className="w-5 h-5 mr-2" />
                        Criar Template
                    </Button>
                </div>

                {/* Search and Filters */}
                <div className="flex flex-col md:flex-row gap-4 mb-8">
                    <div className="relative flex-1">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                        <Input
                            placeholder="Buscar templates..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10"
                        />
                    </div>
                    <Tabs defaultValue="all" onValueChange={setActiveTab}>
                        <TabsList className="bg-white">
                            {templateTypes.map((type) => (
                                <TabsTrigger key={type.value} value={type.value}>
                                    {type.label}
                                </TabsTrigger>
                            ))}
                        </TabsList>
                    </Tabs>
                </div>

                {/* Templates Grid */}
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {isLoading ? (
                        Array(6).fill(0).map((_, i) => (
                            <Card key={i} className="border border-slate-200">
                                <CardContent className="p-6 animate-pulse">
                                    <div className="w-12 h-12 bg-slate-200 rounded-lg mb-4"></div>
                                    <div className="h-5 bg-slate-200 rounded mb-2"></div>
                                    <div className="h-4 bg-slate-200 rounded mb-4"></div>
                                    <div className="h-8 bg-slate-200 rounded"></div>
                                </CardContent>
                            </Card>
                        ))
                    ) : filteredTemplates.length === 0 ? (
                        <div className="col-span-full text-center py-12">
                            <FileText className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                            <h3 className="text-lg font-semibold text-slate-900 mb-2">
                                Nenhum template encontrado
                            </h3>
                            <p className="text-slate-600">
                                Tente ajustar os filtros ou criar um novo template
                            </p>
                        </div>
                    ) : (
                        filteredTemplates.map((template) => (
                            <Card
                                key={template.id}
                                className="border border-slate-200 hover:border-navy-300 hover:shadow-lg transition-all duration-300 group"
                            >
                                <CardHeader className="pb-4">
                                    <div className="flex items-start justify-between">
                                        <div className="w-12 h-12 bg-navy-100 rounded-lg flex items-center justify-center mb-4">
                                            <FileText className="w-6 h-6 text-navy-600" />
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Badge variant="outline" className="text-xs">
                                                {template.type}
                                            </Badge>
                                            {template.usage_count > 10 && (
                                                <Star className="w-4 h-4 text-yellow-500" />
                                            )}
                                        </div>
                                    </div>
                                    <CardTitle className="text-lg font-bold text-slate-900 group-hover:text-navy-600 transition-colors">
                                        {template.name}
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="pt-0">
                                    <p className="text-slate-600 text-sm mb-4 leading-relaxed">
                                        {template.description}
                                    </p>
                                    <div className="flex gap-2">
                                        <Button variant="outline" size="sm" className="flex-1">
                                            <Eye className="w-4 h-4 mr-2" />
                                            Pré-visualizar
                                        </Button>
                                        <Button size="sm" className="bg-navy-600 hover:bg-navy-700">
                                            <Download className="w-4 h-4 mr-2" />
                                            Usar
                                        </Button>
                                    </div>
                                    <div className="flex items-center justify-between mt-3 text-xs text-slate-500">
                                        <span>Usado {template.usage_count || 0}x</span>
                                        <span>Público</span>
                                    </div>
                                </CardContent>
                            </Card>
                        ))
                    )}
                </div>
            </div>
        </div>
    );
}
