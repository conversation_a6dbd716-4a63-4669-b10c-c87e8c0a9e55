import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { createPageUrl } from "@/utils";
import {
    ArrowRight,
    BarChart3,
    FileText,
    HelpCircle,
    PieChart,
    Radar,
    Route,
    Sparkles,
    Target,
    Users
} from "lucide-react";
import React from "react";
import { Link } from "react-router-dom";

const tools = [
    {
        id: "intake_radar",
        name: "Intake Radar",
        description: "Transforme conversas soltas em briefings estruturados",
        icon: Radar,
        color: "from-blue-500 to-cyan-500",
        demo_url: "#"
    },
    {
        id: "briefing_builder",
        name: "Briefing Builder",
        description: "Crie briefings completos com todas as seções necessárias",
        icon: FileText,
        color: "from-green-500 to-emerald-500",
        demo_url: "#"
    },
    {
        id: "critical_questions",
        name: "Perguntas Críticas",
        description: "Identifique gaps no briefing antes de começar a campanha",
        icon: HelpCircle,
        color: "from-orange-500 to-red-500",
        demo_url: "#"
    },
    {
        id: "personas",
        name: "Personas",
        description: "Desenvolva personas detalhadas baseadas no seu público",
        icon: Users,
        color: "from-purple-500 to-pink-500",
        demo_url: "#"
    },
    {
        id: "journey",
        name: "Jornada",
        description: "Mapeie a jornada completa do seu cliente ideal",
        icon: Route,
        color: "from-indigo-500 to-purple-500",
        demo_url: "#"
    },
    {
        id: "media_planner",
        name: "Media Planner",
        description: "Distribua verba entre canais com justificativa clara",
        icon: Target,
        color: "from-teal-500 to-green-500",
        demo_url: "#"
    },
    {
        id: "kpis_utm",
        name: "KPIs e UTM",
        description: "Configure métricas e tracking para sua campanha",
        icon: BarChart3,
        color: "from-amber-500 to-orange-500",
        demo_url: "#"
    },
    {
        id: "reports",
        name: "Relatórios",
        description: "Gere relatórios com insights acionáveis automaticamente",
        icon: PieChart,
        color: "from-rose-500 to-pink-500",
        demo_url: "#"
    }
];

export default function Tools() {
    return (
        <div className="min-h-screen">
            {/* Header */}
            <section className="bg-gradient-to-br from-navy-600 to-slate-800 text-white py-20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <Badge className="mb-6 bg-white/20 text-white border-white/30">
                            <Sparkles className="w-4 h-4 mr-2" />
                            8 agentes especializados
                        </Badge>
                        <h1 className="text-4xl md:text-5xl font-bold mb-6">
                            Ferramentas que aceleram
                            <span className="block text-teal-400">seu processo criativo</span>
                        </h1>
                        <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                            Cada ferramenta foi criada para resolver um problema específico do dia a dia
                            de agências e profissionais de marketing.
                        </p>
                    </div>
                </div>
            </section>

            {/* Tools Grid */}
            <section className="py-20 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        {tools.map((tool) => (
                            <Card
                                key={tool.id}
                                className="border border-slate-200 hover:border-navy-300 hover:shadow-xl transition-all duration-300 group cursor-pointer"
                            >
                                <CardHeader className="pb-4">
                                    <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${tool.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200`}>
                                        <tool.icon className="w-6 h-6 text-white" />
                                    </div>
                                    <CardTitle className="text-lg font-bold text-slate-900 group-hover:text-navy-600 transition-colors">
                                        {tool.name}
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="pt-0">
                                    <p className="text-slate-600 text-sm mb-4 leading-relaxed">
                                        {tool.description}
                                    </p>
                                    <Button
                                        variant="ghost"
                                        className="w-full justify-between text-navy-600 hover:bg-navy-50 group-hover:bg-navy-50"
                                    >
                                        Ver demo
                                        <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
                                    </Button>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>

            {/* How it Works */}
            <section className="py-20 bg-slate-50">
                <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
                            Como funciona na prática
                        </h2>
                        <p className="text-lg text-slate-600">
                            Processo simples, resultados profissionais
                        </p>
                    </div>

                    <div className="grid md:grid-cols-3 gap-12">
                        <div className="text-center">
                            <div className="w-16 h-16 bg-navy-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                                <span className="text-2xl font-bold text-white">1</span>
                            </div>
                            <h3 className="text-xl font-bold text-slate-900 mb-3">
                                Conte seu objetivo
                            </h3>
                            <p className="text-slate-600">
                                Descreva o que precisa em linguagem natural.
                                Sem formulários complicados.
                            </p>
                        </div>

                        <div className="text-center">
                            <div className="w-16 h-16 bg-teal-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                                <span className="text-2xl font-bold text-white">2</span>
                            </div>
                            <h3 className="text-xl font-bold text-slate-900 mb-3">
                                IA trabalha para você
                            </h3>
                            <p className="text-slate-600">
                                Agentes especializados criam conteúdo profissional
                                baseado em melhores práticas.
                            </p>
                        </div>

                        <div className="text-center">
                            <div className="w-16 h-16 bg-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                                <span className="text-2xl font-bold text-white">3</span>
                            </div>
                            <h3 className="text-xl font-bold text-slate-900 mb-3">
                                Revise e exporte
                            </h3>
                            <p className="text-slate-600">
                                Ajuste o resultado, exporte em PDF ou envie
                                direto para o cliente.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-20 bg-gradient-to-r from-navy-600 to-slate-700">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                        Experimente todas as ferramentas
                    </h2>
                    <p className="text-xl text-navy-100 mb-10">
                        Comece grátis. Sem cartão. Sem compromisso.
                    </p>
                    <Link to={createPageUrl("Dashboard")}>
                        <Button
                            size="lg"
                            className="bg-white text-navy-600 hover:bg-slate-50 px-8 py-4 text-lg font-semibold rounded-xl shadow-lg"
                        >
                            Começar agora
                            <ArrowRight className="w-5 h-5 ml-2" />
                        </Button>
                    </Link>
                </div>
            </section>
        </div>
    );
}