
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    AlertCircle,
    BarChart3,
    CheckCircle,
    Chrome,
    ExternalLink,
    FileText // Added FileText import
    ,
    Settings,
    Target,
    Users
} from "lucide-react";
import React, { useState } from "react";

const integrations = [
    {
        id: "google_login",
        name: "Google Login",
        description: "Autenticação segura via Google",
        icon: Chrome,
        color: "from-red-500 to-orange-500",
        status: "connected",
        category: "Autenticação"
    },
    {
        id: "google_docs",
        name: "Google Docs",
        description: "Exporte briefings e relatórios direto para o Drive",
        icon: FileText,
        color: "from-blue-500 to-blue-600",
        status: "available",
        category: "Produtividade"
    },
    {
        id: "google_analytics",
        name: "Google Analytics 4",
        description: "Importe dados de performance para relatórios",
        icon: BarChart3,
        color: "from-green-500 to-green-600",
        status: "available",
        category: "Analytics"
    },
    {
        id: "google_ads",
        name: "Google Ads",
        description: "Conecte campanhas e importe métricas automaticamente",
        icon: Target,
        color: "from-yellow-500 to-orange-500",
        status: "coming_soon",
        category: "Mídia Paga"
    },
    {
        id: "meta_ads",
        name: "Meta Ads",
        description: "Integração com Facebook e Instagram Ads",
        icon: Users,
        color: "from-blue-600 to-purple-600",
        status: "coming_soon",
        category: "Mídia Paga"
    }
];

const statusConfig = {
    connected: {
        label: "Conectado",
        color: "bg-green-100 text-green-700 border-green-200",
        button: "Desconectar",
        buttonVariant: "outline"
    },
    available: {
        label: "Disponível",
        color: "bg-blue-100 text-blue-700 border-blue-200",
        button: "Conectar",
        buttonVariant: "default"
    },
    coming_soon: {
        label: "Em breve",
        color: "bg-slate-100 text-slate-700 border-slate-200",
        button: "Notificar",
        buttonVariant: "ghost"
    }
};

export default function Integrations() {
    const [connections, setConnections] = useState({
        google_login: "connected",
        google_docs: "available",
        google_analytics: "available",
        google_ads: "coming_soon",
        meta_ads: "coming_soon"
    });

    const handleConnect = (integrationId) => {
        if (connections[integrationId] === "available") {
            setConnections(prev => ({
                ...prev,
                [integrationId]: "connected"
            }));
        }
    };

    const groupedIntegrations = integrations.reduce((acc, integration) => {
        if (!acc[integration.category]) {
            acc[integration.category] = [];
        }
        acc[integration.category].push(integration);
        return acc;
    }, {});

    return (
        <div className="p-6 lg:p-8 bg-slate-50 min-h-screen">
            <div className="max-w-6xl mx-auto">
                {/* Header */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-slate-900 mb-2">Integrações</h1>
                    <p className="text-slate-600">
                        Conecte uTulz com suas ferramentas favoritas para um fluxo mais eficiente
                    </p>
                </div>

                {/* Integration Categories */}
                <div className="space-y-8">
                    {Object.entries(groupedIntegrations).map(([category, categoryIntegrations]) => (
                        <div key={category}>
                            <h2 className="text-xl font-bold text-slate-900 mb-6">{category}</h2>
                            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {categoryIntegrations.map((integration) => {
                                    const status = connections[integration.id] || integration.status;
                                    const config = statusConfig[status];

                                    return (
                                        <Card
                                            key={integration.id}
                                            className="border border-slate-200 hover:border-navy-300 hover:shadow-lg transition-all duration-300"
                                        >
                                            <CardHeader className="pb-4">
                                                <div className="flex items-start justify-between">
                                                    <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${integration.color} flex items-center justify-center mb-4`}>
                                                        <integration.icon className="w-6 h-6 text-white" />
                                                    </div>
                                                    <Badge className={config.color}>
                                                        {status === "connected" && <CheckCircle className="w-3 h-3 mr-1" />}
                                                        {status === "coming_soon" && <AlertCircle className="w-3 h-3 mr-1" />}
                                                        {config.label}
                                                    </Badge>
                                                </div>
                                                <CardTitle className="text-lg font-bold text-slate-900">
                                                    {integration.name}
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent className="pt-0">
                                                <p className="text-slate-600 text-sm mb-6 leading-relaxed">
                                                    {integration.description}
                                                </p>
                                                <Button
                                                    variant={config.buttonVariant}
                                                    className={`w-full ${config.buttonVariant === "default"
                                                            ? "bg-navy-600 hover:bg-navy-700"
                                                            : ""
                                                        }`}
                                                    onClick={() => handleConnect(integration.id)}
                                                    disabled={status === "coming_soon"}
                                                >
                                                    {config.button}
                                                    {status === "available" && <ExternalLink className="w-4 h-4 ml-2" />}
                                                </Button>
                                            </CardContent>
                                        </Card>
                                    );
                                })}
                            </div>
                        </div>
                    ))}
                </div>

                {/* Help Section */}
                <Card className="mt-12 border border-slate-200 bg-navy-50">
                    <CardContent className="p-8 text-center">
                        <Settings className="w-12 h-12 text-navy-600 mx-auto mb-4" />
                        <h3 className="text-xl font-bold text-slate-900 mb-2">
                            Precisa de uma integração específica?
                        </h3>
                        <p className="text-slate-600 mb-6 max-w-2xl mx-auto">
                            Nossa equipe está sempre trabalhando em novas integrações.
                            Sugira ferramentas que você usa no seu dia a dia.
                        </p>
                        <Button variant="outline" className="border-navy-300 text-navy-600 hover:bg-navy-100">
                            Solicitar Integração
                            <ExternalLink className="w-4 h-4 ml-2" />
                        </Button>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
