import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { createPageUrl } from "@/utils";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
    Calendar,
    Download,
    Eye,
    Plus,
    Target,
    Users
} from "lucide-react";
import React from "react";
import { Link } from "react-router-dom";

const statusColors = {
    draft: "bg-slate-100 text-slate-700",
    briefing: "bg-blue-100 text-blue-700",
    strategy: "bg-purple-100 text-purple-700",
    media: "bg-orange-100 text-orange-700",
    monitoring: "bg-teal-100 text-teal-700",
    active: "bg-green-100 text-green-700",
    paused: "bg-yellow-100 text-yellow-700",
    completed: "bg-slate-100 text-slate-700"
};

const statusLabels = {
    draft: "Rascunho",
    briefing: "Briefing",
    strategy: "Estratégia",
    media: "Mídia",
    monitoring: "Monitoramento",
    active: "Ativa",
    paused: "Pausada",
    completed: "Concluída"
};

export default function CampaignsList({ campaigns, isLoading, onRefresh }) {
    return (
        <Card className="border border-slate-200">
            <CardHeader className="border-b border-slate-200">
                <div className="flex justify-between items-center">
                    <CardTitle className="text-xl font-bold text-slate-900">
                        Suas Campanhas
                    </CardTitle>
                    <Link to={createPageUrl("CreateCampaign")}>
                        <Button variant="outline" size="sm">
                            <Plus className="w-4 h-4 mr-2" />
                            Nova
                        </Button>
                    </Link>
                </div>
            </CardHeader>
            <CardContent className="p-6">
                {isLoading ? (
                    <div className="space-y-4">
                        {Array(3).fill(0).map((_, i) => (
                            <div key={i} className="flex justify-between items-center p-4 border border-slate-200 rounded-lg">
                                <div className="flex-1">
                                    <Skeleton className="h-5 w-48 mb-2" />
                                    <Skeleton className="h-4 w-32" />
                                </div>
                                <Skeleton className="h-6 w-16" />
                            </div>
                        ))}
                    </div>
                ) : campaigns.length === 0 ? (
                    <div className="text-center py-12">
                        <Target className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-slate-900 mb-2">
                            Nenhuma campanha criada
                        </h3>
                        <p className="text-slate-600 mb-6">
                            Crie sua primeira campanha e acelere seus resultados
                        </p>
                        <Link to={createPageUrl("CreateCampaign")}>
                            <Button className="bg-navy-600 hover:bg-navy-700">
                                <Plus className="w-5 h-5 mr-2" />
                                Criar primeira campanha
                            </Button>
                        </Link>
                    </div>
                ) : (
                    <div className="space-y-4">
                        {campaigns.map((campaign) => (
                            <div
                                key={campaign.id}
                                className="flex flex-col lg:flex-row justify-between items-start lg:items-center p-4 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors duration-200 gap-4"
                            >
                                <div className="flex-1">
                                    <div className="flex items-center gap-3 mb-2">
                                        <h3 className="font-semibold text-slate-900">{campaign.name}</h3>
                                        <Badge className={statusColors[campaign.status]}>
                                            {statusLabels[campaign.status]}
                                        </Badge>
                                    </div>
                                    <div className="flex items-center gap-4 text-sm text-slate-600">
                                        {campaign.client_name && (
                                            <div className="flex items-center gap-1">
                                                <Users className="w-4 h-4" />
                                                {campaign.client_name}
                                            </div>
                                        )}
                                        <div className="flex items-center gap-1">
                                            <Calendar className="w-4 h-4" />
                                            {format(new Date(campaign.created_date), "dd/MM/yyyy", { locale: ptBR })}
                                        </div>
                                    </div>
                                </div>
                                <div className="flex gap-2">
                                    <Link to={createPageUrl(`CampaignEditor?id=${campaign.id}`)}>
                                        <Button variant="outline" size="sm">
                                            <Eye className="w-4 h-4 mr-2" />
                                            Abrir
                                        </Button>
                                    </Link>
                                    <Button variant="ghost" size="sm">
                                        <Download className="w-4 h-4 mr-2" />
                                        Exportar
                                    </Button>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}