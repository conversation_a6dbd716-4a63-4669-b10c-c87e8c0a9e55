import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { createPageUrl } from "@/utils";
import {
    ArrowRight,
    Building2,
    Check,
    Crown,
    Zap
} from "lucide-react";
import React from "react";
import { Link } from "react-router-dom";

const plans = [
    {
        name: "Gratuito",
        price: "R$ 0",
        period: "/mês",
        description: "Perfeito para freelancers e pequenos projetos",
        icon: Zap,
        color: "border-slate-200",
        bgColor: "bg-white",
        features: [
            "5 execuções de agentes por mês",
            "2 campanhas ativas",
            "Templates básicos",
            "Exportação em PDF",
            "Suporte por email"
        ],
        cta: "Começar grátis",
        ctaVariant: "outline"
    },
    {
        name: "Pro",
        price: "R$ 97",
        period: "/mês",
        description: "Ideal para profissionais e pequenas agências",
        icon: Crown,
        color: "border-navy-300 ring-2 ring-navy-200",
        bgColor: "bg-navy-50",
        badge: "Mais popular",
        features: [
            "Agentes ilimitados",
            "Campanhas ilimitadas",
            "Todos os templates",
            "Exportação avançada (PDF, PPTX)",
            "Integrações com GA4 e Google Ads",
            "Histórico completo",
            "Suporte prioritário"
        ],
        cta: "Assinar Pro",
        ctaVariant: "default"
    },
    {
        name: "Agência",
        price: "R$ 297",
        period: "/mês",
        description: "Para agências e equipes que querem escalar",
        icon: Building2,
        color: "border-slate-200",
        bgColor: "bg-white",
        features: [
            "Tudo do Pro",
            "Usuários ilimitados",
            "Templates personalizados",
            "Whitelabel completo",
            "API para integrações",
            "Treinamento dedicado",
            "Account manager"
        ],
        cta: "Falar com vendas",
        ctaVariant: "outline"
    }
];

export default function Pricing() {
    return (
        <div className="min-h-screen">
            {/* Header */}
            <section className="bg-gradient-to-br from-slate-50 to-white py-20">
                <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <Badge className="mb-6 bg-navy-100 text-navy-700 border-navy-200">
                        ✨ Sem pegadinhas, sem taxas ocultas
                    </Badge>
                    <h1 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
                        Preços honestos para
                        <span className="block text-navy-600">resultados reais</span>
                    </h1>
                    <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                        Escolha o plano ideal para o seu negócio.
                        Comece grátis e escale conforme cresce.
                    </p>
                </div>
            </section>

            {/* Pricing Cards */}
            <section className="py-20 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid md:grid-cols-3 gap-8">
                        {plans.map((plan) => (
                            <Card
                                key={plan.name}
                                className={`relative ${plan.color} ${plan.bgColor} hover:shadow-xl transition-all duration-300`}
                            >
                                {plan.badge && (
                                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                        <Badge className="bg-navy-600 text-white">
                                            {plan.badge}
                                        </Badge>
                                    </div>
                                )}

                                <CardHeader className="text-center pb-6">
                                    <div className={`w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-r ${plan.name === "Pro" ? "from-navy-500 to-navy-600" : "from-slate-400 to-slate-500"
                                        } flex items-center justify-center`}>
                                        <plan.icon className="w-6 h-6 text-white" />
                                    </div>
                                    <CardTitle className="text-2xl font-bold text-slate-900">
                                        {plan.name}
                                    </CardTitle>
                                    <div className="mt-4">
                                        <span className="text-4xl font-bold text-slate-900">{plan.price}</span>
                                        <span className="text-slate-600">{plan.period}</span>
                                    </div>
                                    <p className="text-slate-600 mt-2">{plan.description}</p>
                                </CardHeader>

                                <CardContent className="pt-0">
                                    <ul className="space-y-3 mb-8">
                                        {plan.features.map((feature, index) => (
                                            <li key={index} className="flex items-start gap-3">
                                                <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                                                <span className="text-slate-700">{feature}</span>
                                            </li>
                                        ))}
                                    </ul>

                                    <Link to={createPageUrl("Dashboard")}>
                                        <Button
                                            className={`w-full ${plan.ctaVariant === "default"
                                                ? "bg-navy-600 hover:bg-navy-700 text-white"
                                                : "border-slate-300 text-slate-700 hover:bg-slate-50"
                                                }`}
                                            variant={plan.ctaVariant}
                                        >
                                            {plan.cta}
                                            <ArrowRight className="w-4 h-4 ml-2" />
                                        </Button>
                                    </Link>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>

            {/* FAQ Section */}
            <section className="py-20 bg-slate-50">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl font-bold text-slate-900 mb-4">
                            Perguntas frequentes
                        </h2>
                    </div>

                    <div className="space-y-6">
                        <Card className="border border-slate-200">
                            <CardContent className="p-6">
                                <h3 className="font-bold text-slate-900 mb-2">
                                    Posso cancelar a qualquer momento?
                                </h3>
                                <p className="text-slate-600">
                                    Sim, você pode cancelar sua assinatura a qualquer momento.
                                    Não há taxas de cancelamento ou períodos mínimos.
                                </p>
                            </CardContent>
                        </Card>

                        <Card className="border border-slate-200">
                            <CardContent className="p-6">
                                <h3 className="font-bold text-slate-900 mb-2">
                                    Como funciona o período gratuito?
                                </h3>
                                <p className="text-slate-600">
                                    O plano gratuito inclui 5 execuções de agentes por mês e 2 campanhas ativas.
                                    Perfeito para testar todas as funcionalidades.
                                </p>
                            </CardContent>
                        </Card>

                        <Card className="border border-slate-200">
                            <CardContent className="p-6">
                                <h3 className="font-bold text-slate-900 mb-2">
                                    Vocês oferecem treinamento?
                                </h3>
                                <p className="text-slate-600">
                                    Planos Pro incluem materiais de treinamento.
                                    Planos Agência incluem sessões dedicadas com nosso time.
                                </p>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </section>
        </div>
    );
}