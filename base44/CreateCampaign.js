import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Campaign } from "@/entities/Campaign";
import { createPageUrl } from "@/utils";
import {
    ArrowLeft,
    ArrowRight,
    DollarSign,
    Rocket,
    Target,
    Users
} from "lucide-react";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";

export default function CreateCampaign() {
    const navigate = useNavigate();
    const [formData, setFormData] = useState({
        name: "",
        client_name: "",
        objective: "",
        target_audience: "",
        budget: "",
        start_date: "",
        end_date: ""
    });
    const [isCreating, setIsCreating] = useState(false);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsCreating(true);

        try {
            const campaign = await Campaign.create({
                ...formData,
                budget: parseFloat(formData.budget) || 0,
                status: "briefing",
                current_step: 1
            });

            navigate(createPageUrl(`CampaignWizard?id=${campaign.id}`));
        } catch (error) {
            console.error("Erro ao criar campanha:", error);
        }

        setIsCreating(false);
    };

    const handleInputChange = (field, value) => {
        setFormData(prev => ({ ...prev, [field]: value }));
    };

    return (
        <div className="min-h-screen bg-slate-50 p-6">
            <div className="max-w-3xl mx-auto">
                {/* Header */}
                <div className="flex items-center gap-4 mb-8">
                    <Button
                        variant="outline"
                        size="icon"
                        onClick={() => navigate(createPageUrl("Dashboard"))}
                    >
                        <ArrowLeft className="w-4 h-4" />
                    </Button>
                    <div>
                        <h1 className="text-2xl md:text-3xl font-bold text-slate-900">
                            Criar Nova Campanha
                        </h1>
                        <p className="text-slate-600 mt-1">
                            Vamos começar com as informações básicas
                        </p>
                    </div>
                </div>

                <Card className="border border-slate-200 shadow-lg">
                    <CardHeader className="border-b border-slate-200">
                        <CardTitle className="flex items-center gap-2 text-xl font-bold">
                            <Rocket className="w-5 h-5 text-navy-600" />
                            Informações da Campanha
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-8">
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="text-sm font-medium text-slate-700">
                                        Nome da Campanha *
                                    </Label>
                                    <Input
                                        id="name"
                                        value={formData.name}
                                        onChange={(e) => handleInputChange("name", e.target.value)}
                                        placeholder="Ex: Lançamento Produto X"
                                        required
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="client_name" className="text-sm font-medium text-slate-700">
                                        Cliente
                                    </Label>
                                    <Input
                                        id="client_name"
                                        value={formData.client_name}
                                        onChange={(e) => handleInputChange("client_name", e.target.value)}
                                        placeholder="Nome do cliente"
                                    />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="objective" className="text-sm font-medium text-slate-700">
                                    <Target className="w-4 h-4 inline mr-2" />
                                    Objetivo Principal *
                                </Label>
                                <Textarea
                                    id="objective"
                                    value={formData.objective}
                                    onChange={(e) => handleInputChange("objective", e.target.value)}
                                    placeholder="Descreva o objetivo principal desta campanha..."
                                    className="h-24"
                                    required
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="target_audience" className="text-sm font-medium text-slate-700">
                                    <Users className="w-4 h-4 inline mr-2" />
                                    Público-Alvo
                                </Label>
                                <Textarea
                                    id="target_audience"
                                    value={formData.target_audience}
                                    onChange={(e) => handleInputChange("target_audience", e.target.value)}
                                    placeholder="Descreva seu público-alvo..."
                                    className="h-20"
                                />
                            </div>

                            <div className="grid md:grid-cols-3 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="budget" className="text-sm font-medium text-slate-700">
                                        <DollarSign className="w-4 h-4 inline mr-2" />
                                        Orçamento (R$)
                                    </Label>
                                    <Input
                                        id="budget"
                                        type="number"
                                        value={formData.budget}
                                        onChange={(e) => handleInputChange("budget", e.target.value)}
                                        placeholder="50000"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="start_date" className="text-sm font-medium text-slate-700">
                                        Data de Início
                                    </Label>
                                    <Input
                                        id="start_date"
                                        type="date"
                                        value={formData.start_date}
                                        onChange={(e) => handleInputChange("start_date", e.target.value)}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="end_date" className="text-sm font-medium text-slate-700">
                                        Data de Fim
                                    </Label>
                                    <Input
                                        id="end_date"
                                        type="date"
                                        value={formData.end_date}
                                        onChange={(e) => handleInputChange("end_date", e.target.value)}
                                    />
                                </div>
                            </div>

                            <div className="flex justify-end gap-4 pt-6">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => navigate(createPageUrl("Dashboard"))}
                                >
                                    Cancelar
                                </Button>
                                <Button
                                    type="submit"
                                    disabled={isCreating || !formData.name || !formData.objective}
                                    className="bg-navy-600 hover:bg-navy-700"
                                >
                                    {isCreating ? "Criando..." : "Criar e Continuar"}
                                    <ArrowRight className="w-4 h-4 ml-2" />
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}