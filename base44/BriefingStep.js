import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { CampaignStep } from "@/entities/CampaignStep";
import { StepHistory } from "@/entities/StepHistory";
import { InvokeLLM } from "@/integrations/Core";
import {
    ArrowRight,
    FileText,
    Loader2
} from "lucide-react";
import React, { useEffect, useState } from "react";

import BriefingEditor from "./BriefingEditor";
import PointsRaised from "./PointsRaised";
import VersionHistory from "./VersionHistory";

export default function BriefingStep({ campaign, campaignStep, onStepComplete, onStepUpdate }) {
    const [formData, setFormData] = useState({
        campaign_name: campaign?.name || "",
        client_name: campaign?.client_name || "",
        date: campaign?.start_date || "",
        budget: campaign?.budget || "",
        target_audience: campaign?.target_audience || "",
        creative_direction: "",
        description: campaign?.objective || ""
    });

    const [isGenerating, setIsGenerating] = useState(false);
    const [generatedData, setGeneratedData] = useState(null);
    const [activeTab, setActiveTab] = useState("form");
    const [errors, setErrors] = useState({});

    useEffect(() => {
        if (campaignStep) {
            setGeneratedData(campaignStep);
            setActiveTab("results");
        }
    }, [campaignStep]);

    const validateForm = () => {
        const newErrors = {};

        if (!formData.description || formData.description.trim().split(' ').length < 10) {
            newErrors.description = "Campo descritivo deve ter pelo menos 10 palavras";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const generateBriefing = async () => {
        if (!validateForm()) return;

        setIsGenerating(true);

        try {
            // Executar agente de briefing
            const result = await InvokeLLM({
                prompt: `Você é um profissional de atendimento de agência de publicidade. Analise as informações fornecidas e crie um briefing completo de campanha.

INFORMAÇÕES DA CAMPANHA:
- Nome: ${formData.campaign_name || "A definir"}
- Cliente: ${formData.client_name || "A definir"}
- Data: ${formData.date || "A definir"}
- Orçamento: ${formData.budget ? `R$ ${formData.budget}` : "A definir"}
- Público-alvo: ${formData.target_audience || "A definir"}
- Direcionamento criativo: ${formData.creative_direction || "A definir"}
- Descrição/Objetivo: ${formData.description}

INSTRUÇÕES:
1. Busque informações reais na internet sobre o cliente, mercado, concorrentes e oportunidades
2. Identifique pontos críticos, dúvidas e itens faltantes
3. Crie um briefing completo e humanizado

Retorne no seguinte formato JSON:`,
                add_context_from_internet: true,
                response_json_schema: {
                    type: "object",
                    properties: {
                        points_raised: {
                            type: "object",
                            properties: {
                                doubts: { type: "array", items: { type: "string" } },
                                missing_items: { type: "array", items: { type: "string" } },
                                critical_questions: { type: "array", items: { type: "string" } },
                                opportunities: { type: "array", items: { type: "string" } },
                                risks: { type: "array", items: { type: "string" } }
                            }
                        },
                        briefing_content: { type: "string", description: "HTML formatado do briefing completo" }
                    }
                }
            });

            // Salvar no banco
            const stepData = {
                campaign_id: campaign.id,
                step_number: 1,
                step_name: "briefing",
                input_data: formData,
                agent_output: result,
                points_raised: result.points_raised,
                briefing_content: result.briefing_content,
                status: "completed",
                version: 1
            };

            let savedStep;
            if (campaignStep) {
                savedStep = await CampaignStep.update(campaignStep.id, stepData);
            } else {
                savedStep = await CampaignStep.create(stepData);
            }

            // Salvar histórico
            await StepHistory.create({
                campaign_step_id: savedStep.id,
                version_number: 1,
                content: result.briefing_content,
                change_type: "agent_generated",
                change_description: "Briefing gerado pelo agente"
            });

            setGeneratedData(savedStep);
            setActiveTab("results");
            onStepUpdate();

        } catch (error) {
            console.error("Erro ao gerar briefing:", error);
        }

        setIsGenerating(false);
    };

    const handleInputChange = (field, value) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: null }));
        }
    };

    return (
        <div className="space-y-6">
            <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-slate-900 dark:text-slate-50 mb-2">
                    Etapa 1: Criação do Briefing
                </h2>
                <p className="text-lg text-slate-600 dark:text-slate-400">
                    Vamos criar um briefing completo para sua campanha
                </p>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="form">Informações</TabsTrigger>
                    <TabsTrigger value="results" disabled={!generatedData}>
                        Resultados
                    </TabsTrigger>
                    <TabsTrigger value="history" disabled={!generatedData}>
                        Histórico
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="form" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <FileText className="w-5 h-5 text-navy-600 dark:text-teal-400" />
                                Informações da Campanha
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="campaign_name">Nome da Campanha</Label>
                                    <Input
                                        id="campaign_name"
                                        value={formData.campaign_name}
                                        onChange={(e) => handleInputChange("campaign_name", e.target.value)}
                                        placeholder="Ex: Lançamento Produto X"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="client_name">Cliente</Label>
                                    <Input
                                        id="client_name"
                                        value={formData.client_name}
                                        onChange={(e) => handleInputChange("client_name", e.target.value)}
                                        placeholder="Nome do cliente"
                                    />
                                </div>
                            </div>

                            <div className="grid md:grid-cols-3 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="date">Data da Campanha</Label>
                                    <Input
                                        id="date"
                                        type="date"
                                        value={formData.date}
                                        onChange={(e) => handleInputChange("date", e.target.value)}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="budget">Orçamento (R$)</Label>
                                    <Input
                                        id="budget"
                                        type="number"
                                        value={formData.budget}
                                        onChange={(e) => handleInputChange("budget", e.target.value)}
                                        placeholder="50000"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="target_audience">Público-alvo</Label>
                                    <Input
                                        id="target_audience"
                                        value={formData.target_audience}
                                        onChange={(e) => handleInputChange("target_audience", e.target.value)}
                                        placeholder="Ex: Millennials urbanos"
                                    />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="creative_direction">Direcionamento Criativo</Label>
                                <Textarea
                                    id="creative_direction"
                                    value={formData.creative_direction}
                                    onChange={(e) => handleInputChange("creative_direction", e.target.value)}
                                    placeholder="Descreva o tom, estilo ou conceitos desejados..."
                                    className="h-20"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description">
                                    Descrição Detalhada *
                                    <span className="text-sm text-slate-500 ml-2">(mínimo 10 palavras)</span>
                                </Label>
                                <Textarea
                                    id="description"
                                    value={formData.description}
                                    onChange={(e) => handleInputChange("description", e.target.value)}
                                    placeholder="Descreva detalhadamente o objetivo, contexto, desafios e expectativas da campanha..."
                                    className="h-32"
                                    required
                                />
                                {errors.description && (
                                    <p className="text-sm text-red-600 dark:text-red-400">{errors.description}</p>
                                )}
                            </div>

                            <div className="flex justify-end gap-4">
                                <Button
                                    onClick={generateBriefing}
                                    disabled={isGenerating}
                                    className="bg-navy-600 hover:bg-navy-700 dark:bg-teal-500 dark:hover:bg-teal-600 dark:text-slate-900"
                                >
                                    {isGenerating ? (
                                        <>
                                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                            Gerando briefing...
                                        </>
                                    ) : (
                                        <>
                                            <FileText className="w-4 h-4 mr-2" />
                                            Gerar Briefing
                                        </>
                                    )}
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="results" className="space-y-6">
                    {generatedData && (
                        <div className="grid lg:grid-cols-3 gap-6">
                            <div className="lg:col-span-2">
                                <BriefingEditor
                                    briefingContent={generatedData.briefing_content}
                                    campaignStep={generatedData}
                                    onContentUpdate={onStepUpdate}
                                />
                            </div>
                            <div>
                                <PointsRaised pointsRaised={generatedData.points_raised} />

                                <div className="mt-6">
                                    <Button
                                        onClick={onStepComplete}
                                        className="w-full bg-navy-600 hover:bg-navy-700 dark:bg-teal-500 dark:hover:bg-teal-600 dark:text-slate-900"
                                    >
                                        Continuar para Planejamento
                                        <ArrowRight className="w-4 h-4 ml-2" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    )}
                </TabsContent>

                <TabsContent value="history">
                    {generatedData && (
                        <VersionHistory campaignStepId={generatedData.id} />
                    )}
                </TabsContent>
            </Tabs>
        </div>
    );
}