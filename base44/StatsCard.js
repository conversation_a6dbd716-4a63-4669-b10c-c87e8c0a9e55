import { Card, CardContent } from "@/components/ui/card";
import { TrendingUp } from "lucide-react";
import React from "react";

export default function StatsCard({ title, value, icon: Icon, color, trend }) {
    return (
        <Card className="border border-slate-200 hover:shadow-lg transition-all duration-300">
            <CardContent className="p-6">
                <div className="flex items-start justify-between">
                    <div className="flex-1">
                        <p className="text-sm font-medium text-slate-600 mb-1">{title}</p>
                        <p className="text-2xl md:text-3xl font-bold text-slate-900">{value}</p>
                        {trend && (
                            <div className="flex items-center mt-3 text-sm">
                                <TrendingUp className="w-4 h-4 mr-1 text-teal-500" />
                                <span className="text-teal-600 font-medium">{trend}</span>
                            </div>
                        )}
                    </div>
                    <div className={`p-3 rounded-xl bg-gradient-to-r ${color} bg-opacity-10`}>
                        <Icon className="w-6 h-6 text-white" style={{
                            filter: `drop-shadow(0 0 0 ${color.includes('navy') ? '#1e293b' : '#14b8a6'})`
                        }} />
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}