import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import {
    AlertCircle,
    AlertTriangle,
    CheckCircle,
    MessageCircle,
    TrendingUp
} from "lucide-react";
import React from "react";

export default function PointsRaised({ pointsRaised }) {
    if (!pointsRaised) return null;

    const sections = [
        {
            key: "doubts",
            title: "Dúvidas Identificadas",
            icon: MessageCircle,
            color: "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400",
            items: pointsRaised.doubts || []
        },
        {
            key: "missing_items",
            title: "Itens Faltantes",
            icon: AlertTriangle,
            color: "bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-400",
            items: pointsRaised.missing_items || []
        },
        {
            key: "critical_questions",
            title: "Perguntas Críticas",
            icon: AlertCircle,
            color: "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400",
            items: pointsRaised.critical_questions || []
        },
        {
            key: "opportunities",
            title: "Oportunidades",
            icon: TrendingUp,
            color: "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400",
            items: pointsRaised.opportunities || []
        },
        {
            key: "risks",
            title: "Riscos Identificados",
            icon: AlertTriangle,
            color: "bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400",
            items: pointsRaised.risks || []
        }
    ];

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                    <CheckCircle className="w-5 h-5 text-navy-600 dark:text-teal-400" />
                    Análise do Agente
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
                {sections.map((section) => (
                    section.items.length > 0 && (
                        <div key={section.key}>
                            <div className="flex items-center gap-2 mb-3">
                                <section.icon className={`w-4 h-4 ${section.color.includes('blue') ? 'text-blue-600' : section.color.includes('orange') ? 'text-orange-600' : section.color.includes('red') ? 'text-red-600' : section.color.includes('green') ? 'text-green-600' : 'text-purple-600'} dark:text-opacity-70`} />
                                <h4 className="font-semibold text-sm text-slate-900 dark:text-slate-100">
                                    {section.title}
                                </h4>
                                <Badge variant="outline" className="text-xs">
                                    {section.items.length}
                                </Badge>
                            </div>
                            <ul className="space-y-2">
                                {section.items.map((item, index) => (
                                    <li
                                        key={index}
                                        className="text-sm text-slate-700 dark:text-slate-300 bg-slate-50 dark:bg-slate-800 p-3 rounded-lg border-l-4 border-slate-200 dark:border-slate-600"
                                    >
                                        {item}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    )
                ))}

                {sections.every(section => section.items.length === 0) && (
                    <div className="text-center py-8 text-slate-500 dark:text-slate-400">
                        <CheckCircle className="w-8 h-8 mx-auto mb-2 text-green-500" />
                        <p className="text-sm">Nenhum ponto crítico identificado</p>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}