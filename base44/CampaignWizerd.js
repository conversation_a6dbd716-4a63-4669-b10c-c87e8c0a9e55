
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Campaign } from "@/entities/Campaign";
import { CampaignStep } from "@/entities/CampaignStep";
import { createPageUrl } from "@/utils";
import {
    ArrowLeft,
    BarChart3,
    CheckCircle,
    Clock,
    Palette,
    Target,
    Users
} from "lucide-react";
import React, { useCallback, useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";

import BriefingStep from "../components/campaign/BriefingStep";

const steps = [
    {
        number: 1,
        name: "briefing",
        title: "Atendimento",
        subtitle: "Criação do Briefing",
        icon: Users,
        color: "from-blue-500 to-cyan-500"
    },
    {
        number: 2,
        name: "planning",
        title: "Planejamento",
        subtitle: "Estratégia e Plano",
        icon: Target,
        color: "from-green-500 to-emerald-500"
    },
    {
        number: 3,
        name: "media",
        title: "<PERSON><PERSON><PERSON>",
        subtitle: "Plano de Mídia",
        icon: BarChart3,
        color: "from-orange-500 to-red-500"
    },
    {
        number: 4,
        name: "creative",
        title: "Criação",
        subtitle: "Conceito e KV",
        icon: Palette,
        color: "from-purple-500 to-pink-500"
    }
];

export default function CampaignWizard() {
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const campaignId = searchParams.get("id");

    const [campaign, setCampaign] = useState(null);
    const [currentStep, setCurrentStep] = useState(1);
    const [campaignSteps, setCampaignSteps] = useState({});
    const [isLoading, setIsLoading] = useState(true);

    const loadCampaign = useCallback(async () => {
        try {
            const data = await Campaign.get(campaignId);
            setCampaign(data);
            setCurrentStep(data.current_step || 1);
        } catch (error) {
            console.error("Erro ao carregar campanha:", error);
            navigate(createPageUrl("Dashboard"));
        }
    }, [campaignId, navigate]);

    const loadCampaignSteps = useCallback(async () => {
        try {
            const steps = await CampaignStep.filter({ campaign_id: campaignId });
            const stepsMap = {};
            steps.forEach(step => {
                stepsMap[step.step_number] = step;
            });
            setCampaignSteps(stepsMap);
        } catch (error) {
            console.error("Erro ao carregar etapas:", error);
        }
        setIsLoading(false);
    }, [campaignId]);

    useEffect(() => {
        if (campaignId) {
            loadCampaign();
            loadCampaignSteps();
        }
    }, [campaignId, loadCampaign, loadCampaignSteps]);

    const updateCampaignStep = async (stepNumber, status) => {
        await Campaign.update(campaignId, {
            current_step: stepNumber,
            status: steps[stepNumber - 1]?.name || "briefing"
        });
        setCurrentStep(stepNumber);
        setCampaign(prev => ({ ...prev, current_step: stepNumber }));
    };

    const getStepStatus = (stepNumber) => {
        if (stepNumber < currentStep) return "completed";
        if (stepNumber === currentStep) return "active";
        return "pending";
    };

    const progress = ((currentStep - 1) / (steps.length - 1)) * 100;

    if (isLoading) {
        return (
            <div className="min-h-screen bg-slate-50 dark:bg-slate-950 p-6 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-navy-600 mx-auto mb-4"></div>
                    <p className="text-slate-600 dark:text-slate-400">Carregando campanha...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-slate-50 dark:bg-slate-950">
            {/* Header */}
            <div className="bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-800 px-6 py-4">
                <div className="max-w-7xl mx-auto">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Button
                                variant="outline"
                                size="icon"
                                onClick={() => navigate(createPageUrl("Dashboard"))}
                            >
                                <ArrowLeft className="w-4 h-4" />
                            </Button>
                            <div>
                                <h1 className="text-xl font-bold text-slate-900 dark:text-slate-50">
                                    {campaign?.name || "Nova Campanha"}
                                </h1>
                                <p className="text-sm text-slate-600 dark:text-slate-400">
                                    {campaign?.client_name && `Cliente: ${campaign.client_name}`}
                                </p>
                            </div>
                        </div>
                        <Badge variant="outline" className="text-xs">
                            Etapa {currentStep} de {steps.length}
                        </Badge>
                    </div>
                </div>
            </div>

            {/* Progress Steps */}
            <div className="bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-800 px-6 py-6">
                <div className="max-w-7xl mx-auto">
                    <div className="mb-4">
                        <Progress value={progress} className="h-2" />
                    </div>
                    <div className="grid grid-cols-4 gap-4">
                        {steps.map((step) => {
                            const status = getStepStatus(step.number);
                            const stepData = campaignSteps[step.number];

                            return (
                                <div
                                    key={step.number}
                                    className={`flex items-center gap-3 p-3 rounded-lg transition-all duration-200 ${status === "active"
                                            ? "bg-navy-50 dark:bg-slate-800 border-2 border-navy-300 dark:border-teal-700"
                                            : status === "completed"
                                                ? "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800"
                                                : "bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700"
                                        }`}
                                >
                                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${status === "completed"
                                            ? "bg-green-500"
                                            : status === "active"
                                                ? `bg-gradient-to-r ${step.color}`
                                                : "bg-slate-300 dark:bg-slate-600"
                                        }`}>
                                        {status === "completed" ? (
                                            <CheckCircle className="w-5 h-5 text-white" />
                                        ) : status === "active" ? (
                                            <step.icon className="w-5 h-5 text-white" />
                                        ) : (
                                            <Clock className="w-5 h-5 text-slate-500" />
                                        )}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <h3 className={`font-semibold text-sm ${status === "active"
                                                ? "text-navy-700 dark:text-teal-400"
                                                : status === "completed"
                                                    ? "text-green-700 dark:text-green-400"
                                                    : "text-slate-500 dark:text-slate-400"
                                            }`}>
                                            {step.title}
                                        </h3>
                                        <p className="text-xs text-slate-600 dark:text-slate-400 truncate">
                                            {step.subtitle}
                                        </p>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </div>

            {/* Step Content */}
            <div className="p-6">
                <div className="max-w-7xl mx-auto">
                    {currentStep === 1 && (
                        <BriefingStep
                            campaign={campaign}
                            campaignStep={campaignSteps[1]}
                            onStepComplete={() => updateCampaignStep(2)}
                            onStepUpdate={loadCampaignSteps}
                        />
                    )}
                    {currentStep === 2 && (
                        <div className="text-center py-20">
                            <h2 className="text-2xl font-bold text-slate-900 dark:text-slate-50 mb-4">
                                Etapa de Planejamento
                            </h2>
                            <p className="text-slate-600 dark:text-slate-400 mb-6">
                                Em desenvolvimento - será implementada na próxima iteração
                            </p>
                            <Button onClick={() => updateCampaignStep(3)}>
                                Continuar para Mídia
                            </Button>
                        </div>
                    )}
                    {currentStep === 3 && (
                        <div className="text-center py-20">
                            <h2 className="text-2xl font-bold text-slate-900 dark:text-slate-50 mb-4">
                                Etapa de Mídia
                            </h2>
                            <p className="text-slate-600 dark:text-slate-400 mb-6">
                                Em desenvolvimento - será implementada na próxima iteração
                            </p>
                            <Button onClick={() => updateCampaignStep(4)}>
                                Continuar para Criação
                            </Button>
                        </div>
                    )}
                    {currentStep === 4 && (
                        <div className="text-center py-20">
                            <h2 className="text-2xl font-bold text-slate-900 dark:text-slate-50 mb-4">
                                Etapa de Criação
                            </h2>
                            <p className="text-slate-600 dark:text-slate-400 mb-6">
                                Em desenvolvimento - será implementada na próxima iteração
                            </p>
                            <Button onClick={() => navigate(createPageUrl("Dashboard"))}>
                                Finalizar Campanha
                            </Button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
