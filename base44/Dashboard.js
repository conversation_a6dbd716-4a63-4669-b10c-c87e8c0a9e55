
import { But<PERSON> } from "@/components/ui/button";
import { AgentRun } from "@/entities/AgentRun";
import { Campaign } from "@/entities/Campaign";
import { User } from "@/entities/User";
import { createPageUrl } from "@/utils";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
    BarChart3,
    Calendar,
    FileText,
    Plus,
    Zap
} from "lucide-react";
import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";

import CampaignsList from "../components/dashboard/CampaignsList";
import QuickAgents from "../components/dashboard/QuickAgents";
import StatsCard from "../components/dashboard/StatsCard";

export default function Dashboard() {
    const [campaigns, setCampaigns] = useState([]);
    const [agentRuns, setAgentRuns] = useState([]);
    const [user, setUser] = useState(null);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        loadDashboardData();
    }, []);

    const loadDashboardData = async () => {
        try {
            const [campaignsData, agentRunsData, userData] = await Promise.all([
                Campaign.list("-created_date", 10),
                AgentRun.list("-created_date", 5),
                User.me()
            ]);
            setCampaigns(campaignsData);
            setAgentRuns(agentRunsData);
            setUser(userData);
        } catch (error) {
            console.error("Erro ao carregar dashboard:", error);
        }
        setIsLoading(false);
    };

    const activeCampaigns = campaigns.filter(c => ["active", "briefing", "strategy", "media"].includes(c.status));
    const completedAgents = agentRuns.filter(r => r.status === "completed").length;
    const lastUpdate = campaigns[0]?.updated_date;

    return (
        <div className="p-6 lg:p-8 bg-slate-50 dark:bg-slate-950 min-h-screen">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-50">
                            Bem-vindo, {user?.full_name?.split(' ')[0] || 'Usuário'}
                        </h1>
                        <p className="text-slate-600 dark:text-slate-400 mt-1">
                            Gerencie suas campanhas e acelere seus resultados
                        </p>
                    </div>
                    <Link to={createPageUrl("CreateCampaign")}>
                        <Button className="bg-navy-600 hover:bg-navy-700 dark:bg-teal-500 dark:hover:bg-teal-600 dark:text-slate-900">
                            <Plus className="w-5 h-5 mr-2" />
                            Nova Campanha
                        </Button>
                    </Link>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <StatsCard
                        title="Campanhas Ativas"
                        value={activeCampaigns.length}
                        icon={Zap}
                        color="from-navy-500 to-navy-600"
                        trend="+2 esta semana"
                    />
                    <StatsCard
                        title="Agentes Executados"
                        value={completedAgents}
                        icon={BarChart3}
                        color="from-teal-500 to-teal-600"
                        trend="15 este mês"
                    />
                    <StatsCard
                        title="Templates Salvos"
                        value="12"
                        icon={FileText}
                        color="from-purple-500 to-purple-600"
                        trend="3 novos"
                    />
                    <StatsCard
                        title="Última Atualização"
                        value={lastUpdate ? format(new Date(lastUpdate), "dd/MM", { locale: ptBR }) : "Hoje"}
                        icon={Calendar}
                        color="from-orange-500 to-orange-600"
                    />
                </div>

                <div className="grid lg:grid-cols-3 gap-8">
                    {/* Campaigns List */}
                    <div className="lg:col-span-2">
                        <CampaignsList
                            campaigns={campaigns}
                            isLoading={isLoading}
                            onRefresh={loadDashboardData}
                        />
                    </div>

                    {/* Quick Agents */}
                    <div>
                        <QuickAgents />
                    </div>
                </div>
            </div>
        </div>
    );
}
