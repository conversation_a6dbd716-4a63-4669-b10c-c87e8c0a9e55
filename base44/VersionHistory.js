
import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { StepHistory } from "@/entities/StepHistory";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
    Bot,
    Eye,
    History,
    RefreshCw,
    User
} from "lucide-react";
import React, { useCallback, useEffect, useState } from "react";

export default function VersionHistory({ campaignStepId }) {
    const [history, setHistory] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [selectedVersion, setSelectedVersion] = useState(null);

    const loadHistory = useCallback(async () => {
        try {
            const data = await StepHistory.filter(
                { campaign_step_id: campaignStepId },
                "-version_number"
            );
            setHistory(data);
        } catch (error) {
            console.error("Erro ao carregar histórico:", error);
        }
        setIsLoading(false);
    }, [campaignStepId]); // campaignStepId is a dependency for useCallback

    useEffect(() => {
        if (campaignStepId) {
            loadHistory();
        }
    }, [campaignStepId, loadHistory]); // loadHistory is now a stable dependency due to useCallback

    const getChangeTypeIcon = (changeType) => {
        switch (changeType) {
            case "agent_generated":
                return <Bot className="w-4 h-4 text-blue-600 dark:text-blue-400" />;
            case "manual_edit":
                return <User className="w-4 h-4 text-green-600 dark:text-green-400" />;
            case "agent_regenerated":
                return <RefreshCw className="w-4 h-4 text-orange-600 dark:text-orange-400" />;
            default:
                return <History className="w-4 h-4 text-slate-600 dark:text-slate-400" />;
        }
    };

    const getChangeTypeColor = (changeType) => {
        switch (changeType) {
            case "agent_generated":
                return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400";
            case "manual_edit":
                return "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400";
            case "agent_regenerated":
                return "bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-400";
            default:
                return "bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-400";
        }
    };

    const getChangeTypeLabel = (changeType) => {
        switch (changeType) {
            case "agent_generated":
                return "Gerado por IA";
            case "manual_edit":
                return "Editado manualmente";
            case "agent_regenerated":
                return "Regenerado por IA";
            default:
                return "Alteração";
        }
    };

    return (
        <div className="grid lg:grid-cols-2 gap-6">
            {/* Lista de versões */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <History className="w-5 h-5 text-navy-600 dark:text-teal-400" />
                        Histórico de Versões
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {isLoading ? (
                        <div className="space-y-3">
                            {Array(3).fill(0).map((_, i) => (
                                <div key={i} className="animate-pulse">
                                    <div className="h-16 bg-slate-200 dark:bg-slate-700 rounded-lg"></div>
                                </div>
                            ))}
                        </div>
                    ) : history.length === 0 ? (
                        <div className="text-center py-8 text-slate-500 dark:text-slate-400">
                            <History className="w-8 h-8 mx-auto mb-2" />
                            <p className="text-sm">Nenhum histórico encontrado</p>
                        </div>
                    ) : (
                        <div className="space-y-3">
                            {history.map((version) => (
                                <div
                                    key={version.id}
                                    className={`p-4 rounded-lg border transition-all duration-200 cursor-pointer ${selectedVersion?.id === version.id
                                        ? "border-navy-300 dark:border-teal-700 bg-navy-50 dark:bg-slate-800"
                                        : "border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800"
                                        }`}
                                    onClick={() => setSelectedVersion(version)}
                                >
                                    <div className="flex justify-between items-start mb-2">
                                        <div className="flex items-center gap-2">
                                            {getChangeTypeIcon(version.change_type)}
                                            <span className="font-semibold text-sm text-slate-900 dark:text-slate-100">
                                                Versão {version.version_number}
                                            </span>
                                        </div>
                                        <Badge className={getChangeTypeColor(version.change_type)}>
                                            {getChangeTypeLabel(version.change_type)}
                                        </Badge>
                                    </div>
                                    <p className="text-sm text-slate-600 dark:text-slate-400 mb-2">
                                        {version.change_description}
                                    </p>
                                    <p className="text-xs text-slate-500 dark:text-slate-400">
                                        {format(new Date(version.created_date), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}
                                    </p>
                                </div>
                            ))}
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Preview da versão selecionada */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Eye className="w-5 h-5 text-navy-600 dark:text-teal-400" />
                        {selectedVersion ? `Versão ${selectedVersion.version_number}` : "Selecione uma versão"}
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {selectedVersion ? (
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <Badge className={getChangeTypeColor(selectedVersion.change_type)}>
                                    {getChangeTypeLabel(selectedVersion.change_type)}
                                </Badge>
                                <span className="text-xs text-slate-500 dark:text-slate-400">
                                    {format(new Date(selectedVersion.created_date), "dd/MM/yyyy HH:mm", { locale: ptBR })}
                                </span>
                            </div>

                            <div
                                className="prose prose-slate dark:prose-invert max-w-none bg-white dark:bg-slate-900 p-4 rounded-lg border border-slate-200 dark:border-slate-700 max-h-96 overflow-y-auto"
                                dangerouslySetInnerHTML={{ __html: selectedVersion.content }}
                            />
                        </div>
                    ) : (
                        <div className="text-center py-12 text-slate-500 dark:text-slate-400">
                            <Eye className="w-12 h-12 mx-auto mb-4 opacity-50" />
                            <p>Clique em uma versão para visualizar o conteúdo</p>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
