
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { createPageUrl } from "@/utils";
import {
    ArrowRight,
    BarChart3,
    FileText,
    Globe,
    Target,
    TrendingUp,
    Users
} from "lucide-react";
import React from "react";
import { Link } from "react-router-dom";

export default function Home() {
    return (
        <div className="min-h-screen text-slate-800 dark:text-slate-300">
            {/* Hero Section */}
            <section className="relative overflow-hidden bg-gradient-to-br from-slate-50 via-white to-teal-50 dark:from-slate-950 dark:via-slate-900 dark:to-navy-900">
                <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1553877522-43269d4ea984?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')] bg-cover bg-center opacity-5"></div>
                <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
                    <div className="text-center">
                        <Badge className="mb-6 bg-navy-100 text-navy-700 border-navy-200 dark:bg-slate-800 dark:text-teal-400 dark:border-slate-700">
                            ✨ Acelere suas campanhas com IA
                        </Badge>
                        <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-slate-900 dark:text-slate-50 mb-6 leading-tight">
                            Suas ferramentas para
                            <span className="block bg-gradient-to-r from-navy-600 to-teal-500 dark:from-teal-400 dark:to-cyan-400 bg-clip-text text-transparent">
                                campanhas vencedoras
                            </span>
                        </h1>
                        <p className="text-xl text-slate-600 dark:text-slate-400 mb-12 max-w-3xl mx-auto leading-relaxed">
                            Automatize briefings, estratégias, planos de mídia e relatórios.
                            Menos tempo no operacional, mais foco no que importa.
                        </p>

                        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                            <Link to={createPageUrl("Dashboard")}>
                                <Button
                                    size="lg"
                                    className="bg-navy-600 hover:bg-navy-700 text-white dark:bg-teal-500 dark:hover:bg-teal-600 dark:text-slate-900 px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                                >
                                    Criar Campanha
                                    <ArrowRight className="w-5 h-5 ml-2" />
                                </Button>
                            </Link>
                            <Link to={createPageUrl("Tools")}>
                                <Button
                                    variant="outline"
                                    size="lg"
                                    className="border-slate-300 text-slate-700 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-300 dark:hover:bg-slate-800 px-8 py-4 text-lg font-semibold rounded-xl"
                                >
                                    Ver Ferramentas
                                </Button>
                            </Link>
                        </div>
                    </div>
                </div>
            </section>

            {/* Value Proposition Cards */}
            <section className="py-20 bg-white dark:bg-slate-900">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-slate-50 mb-4">
                            Por que profissionais escolhem uTulz
                        </h2>
                        <p className="text-lg text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
                            Desenvolvido por publicitários, para publicitários. Cada ferramenta resolve um problema real.
                        </p>
                    </div>

                    <div className="grid md:grid-cols-3 gap-8">
                        <Card className="border border-slate-200 dark:border-slate-800 bg-white dark:bg-slate-900 hover:border-navy-300 dark:hover:border-teal-700 hover:shadow-lg transition-all duration-300 group">
                            <CardContent className="p-8 text-center">
                                <div className="w-16 h-16 bg-navy-100 dark:bg-slate-800 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-navy-200 dark:group-hover:bg-slate-700 transition-colors">
                                    <FileText className="w-8 h-8 text-navy-600 dark:text-teal-400" />
                                </div>
                                <h3 className="text-xl font-bold text-slate-900 dark:text-slate-100 mb-3">
                                    Briefing sem novela
                                </h3>
                                <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
                                    Transforme conversas soltas em briefings estruturados.
                                    Identifique gaps automaticamente.
                                </p>
                            </CardContent>
                        </Card>

                        <Card className="border border-slate-200 dark:border-slate-800 bg-white dark:bg-slate-900 hover:border-navy-300 dark:hover:border-teal-700 hover:shadow-lg transition-all duration-300 group">
                            <CardContent className="p-8 text-center">
                                <div className="w-16 h-16 bg-teal-100 dark:bg-slate-800 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-teal-200 dark:group-hover:bg-slate-700 transition-colors">
                                    <Target className="w-8 h-8 text-teal-600 dark:text-teal-400" />
                                </div>
                                <h3 className="text-xl font-bold text-slate-900 dark:text-slate-100 mb-3">
                                    Plano de mídia que se explica
                                </h3>
                                <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
                                    Distribua verba com justificativa clara.
                                    Cronogramas e KPIs que fazem sentido.
                                </p>
                            </CardContent>
                        </Card>

                        <Card className="border border-slate-200 dark:border-slate-800 bg-white dark:bg-slate-900 hover:border-navy-300 dark:hover:border-teal-700 hover:shadow-lg transition-all duration-300 group">
                            <CardContent className="p-8 text-center">
                                <div className="w-16 h-16 bg-purple-100 dark:bg-slate-800 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-purple-200 dark:group-hover:bg-slate-700 transition-colors">
                                    <BarChart3 className="w-8 h-8 text-purple-600 dark:text-purple-400" />
                                </div>
                                <h3 className="text-xl font-bold text-slate-900 dark:text-slate-100 mb-3">
                                    Relatório que ensina algo
                                </h3>
                                <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
                                    Análises que vão além dos números.
                                    Insights acionáveis para otimizar campanhas.
                                </p>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </section>

            {/* Social Proof */}
            <section className="py-20 bg-slate-50 dark:bg-slate-950">
                <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-12">
                        <h2 className="text-2xl md:text-3xl font-bold text-slate-900 dark:text-slate-50 mb-4">
                            Confiado por equipes vencedoras
                        </h2>
                    </div>

                    <div className="grid md:grid-cols-3 gap-8 text-center">
                        <div className="flex flex-col items-center">
                            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mb-4">
                                <TrendingUp className="w-6 h-6 text-green-600 dark:text-green-400" />
                            </div>
                            <h3 className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-2">75%</h3>
                            <p className="text-slate-600 dark:text-slate-400">Redução no tempo de briefing</p>
                        </div>
                        <div className="flex flex-col items-center">
                            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mb-4">
                                <Users className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                            </div>
                            <h3 className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-2">200+</h3>
                            <p className="text-slate-600 dark:text-slate-400">Agências usando diariamente</p>
                        </div>
                        <div className="flex flex-col items-center">
                            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center mb-4">
                                <Globe className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                            </div>
                            <h3 className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-2">500+</h3>
                            <p className="text-slate-600 dark:text-slate-400">Campanhas criadas este mês</p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Final CTA */}
            <section className="py-20 bg-navy-600 dark:bg-teal-600">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h2 className="text-3xl md:text-4xl font-bold text-white dark:text-slate-900 mb-6">
                        Pronto para acelerar suas campanhas?
                    </h2>
                    <p className="text-xl text-navy-100 dark:text-slate-800 mb-10 max-w-2xl mx-auto">
                        Junte-se às agências que já economizam horas toda semana com uTulz.
                    </p>
                    <Link to={createPageUrl("Dashboard")}>
                        <Button
                            size="lg"
                            className="bg-white text-navy-600 hover:bg-slate-50 dark:bg-slate-900 dark:text-white dark:hover:bg-slate-800 px-8 py-4 text-lg font-semibold rounded-xl shadow-lg"
                        >
                            Começar agora — é gratuito
                            <ArrowRight className="w-5 h-5 ml-2" />
                        </Button>
                    </Link>
                </div>
            </section>
        </div>
    );
}
