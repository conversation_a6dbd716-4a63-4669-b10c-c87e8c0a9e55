{"name": "<PERSON><PERSON><PERSON>", "type": "object", "properties": {"campaign_id": {"type": "string", "description": "ID da campanha relacionada"}, "agent_type": {"type": "string", "enum": ["intake_radar", "briefing_builder", "critical_questions", "personas", "journey", "media_planner", "kpis_utm", "reports"], "description": "Tipo do agente executado"}, "input_data": {"type": "object", "description": "Dados de entrada fornecidos ao agente"}, "output_content": {"type": "string", "description": "Conte<PERSON><PERSON> gerado pelo agente"}, "status": {"type": "string", "enum": ["running", "completed", "error"], "default": "running", "description": "Status da execução"}, "execution_time": {"type": "number", "description": "Tempo de execução em segundos"}, "assumptions": {"type": "array", "items": {"type": "string"}, "description": "Suposições feitas pelo agente"}, "questions": {"type": "array", "items": {"type": "string"}, "description": "Dúvidas levantadas pelo agente"}}, "required": ["agent_type"]}