{"name": "CampaignStep", "type": "object", "properties": {"campaign_id": {"type": "string", "description": "ID da campanha relacionada"}, "step_number": {"type": "number", "enum": [1, 2, 3, 4], "description": "Número da etapa (1=Briefing, 2=Planejamento, 3=Mídia, 4=Criação)"}, "step_name": {"type": "string", "enum": ["briefing", "planning", "media", "creative"], "description": "Nome da etapa"}, "input_data": {"type": "object", "description": "Dados de entrada do usuário para a etapa"}, "agent_output": {"type": "object", "description": "Saída gerada pelo agente"}, "points_raised": {"type": "object", "description": "Pontos levantados, d<PERSON><PERSON>as, itens faltantes"}, "briefing_content": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> do briefing em HTML"}, "presentation_content": {"type": "string", "description": "Conteúdo da apresentação"}, "status": {"type": "string", "enum": ["draft", "in_progress", "completed", "needs_revision"], "default": "draft", "description": "Status da etapa"}, "version": {"type": "number", "default": 1, "description": "Versão atual do documento"}, "is_edited": {"type": "boolean", "default": false, "description": "Se foi editado manualmente"}}, "required": ["campaign_id", "step_number", "step_name"]}