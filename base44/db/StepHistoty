{"name": "StepHistory", "type": "object", "properties": {"campaign_step_id": {"type": "string", "description": "ID da etapa da campanha"}, "version_number": {"type": "number", "description": "Númer<PERSON> da <PERSON>ão"}, "content": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "change_type": {"type": "string", "enum": ["agent_generated", "manual_edit", "agent_regenerated"], "description": "Tipo de mudança"}, "change_description": {"type": "string", "description": "Descrição da mudança"}}, "required": ["campaign_step_id", "version_number", "content", "change_type"]}