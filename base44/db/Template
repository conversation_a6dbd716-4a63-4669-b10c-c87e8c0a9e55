{"name": "Template", "type": "object", "properties": {"name": {"type": "string", "description": "Nome do template"}, "type": {"type": "string", "enum": ["briefing", "strategy", "media_plan", "report"], "description": "Tipo do template"}, "content": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> do template em Markdown"}, "description": {"type": "string", "description": "Descrição do template"}, "is_public": {"type": "boolean", "default": true, "description": "Se o template é público ou privado"}, "usage_count": {"type": "number", "default": 0, "description": "Número de vezes que foi usado"}}, "required": ["name", "type", "content"]}